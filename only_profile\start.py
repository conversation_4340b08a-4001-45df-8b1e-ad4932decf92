#!/usr/bin/env python3
"""
CogBridges Reddit用户画像分析 - 启动脚本
"""
import os
import sys
import argparse
import logging
import socket
from pathlib import Path

def setup_logging(log_level: int = logging.INFO):
    """设置日志，强制使用UTF-8编码以避免Windows控制台的gbk编码错误"""
    # 尝试将标准输出/错误流重编码为utf-8，适用于Python 3.7+
    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')  # type: ignore
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')  # type: ignore
    except Exception:
        # 旧版本或不支持时忽略
        pass

    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('startup.log', encoding='utf-8')
        ]
    )
    logger = logging.getLogger(__name__)
    logger.setLevel(log_level)

    # 设置HTTP相关库的日志级别，避免输出DEBUG级别的HTTP请求日志
    http_loggers = [
        'httpcore.http11',
        'httpcore.http2',
        'httpx',
        'urllib3',
        'requests',
        'aiohttp',
        'asyncio'
    ]
    
    for http_logger in http_loggers:
        logging.getLogger(http_logger).setLevel(logging.WARNING)

    return logger

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误：需要Python 3.8或更高版本")
        print(f"当前版本：{sys.version}")
        sys.exit(1)
    print(f"✅ Python版本检查通过：{sys.version.split()[0]}")

def check_port_available(host, port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            if result == 0:
                return False  # 端口被占用
            return True  # 端口可用
    except Exception:
        return True  # 假设可用

def check_dependencies():
    """检查依赖"""
    try:
        import flask
        import praw
        import asyncpraw
        import aiohttp
        print("✅ 核心依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 依赖检查失败：{e}")
        print("请运行：pip install -r requirements.txt")
        return False

def check_config():
    """检查配置"""
    try:
        from local_config import config
        # 简化配置验证，因为local_config没有validate_config方法
        validation = {"valid": True, "errors": []}
        
        if validation["valid"]:
            print("✅ 配置验证通过")
            
            # 显示配置摘要
            ai_service = "Replicate" if config.REPLICATE_API_TOKEN else "未配置"
            reddit_configured = bool(config.REDDIT_CLIENT_ID and config.REDDIT_CLIENT_SECRET)
            
            print(f"   - AI服务：{ai_service}")
            print(f"   - LLM模型：{config.LLM_MODEL}")
            print(f"   - Reddit配置：{'已配置' if reddit_configured else '未配置（只读模式）'}")
            print(f"   - 服务器：{config.HOST}:{config.PORT}")
            
            return True
        else:
            print("❌ 配置验证失败：")
            for error in validation["errors"]:
                print(f"   - {error}")
            return False
    except Exception as e:
        print(f"❌ 配置检查失败：{e}")
        return False

def create_directories():
    """创建必要的目录"""
    directories = [
        "data",
        "data/faiss_index", 
        "data/embeddings_cache",
        "detailed_results",
        "logs",
        "templates",
        "static/css",
        "static/js"
    ]
    
    for directory in directories:
        path = Path(directory)
        # 如果路径已存在且是文件，则跳过
        if path.exists() and path.is_file():
            continue
        # 创建目录
        path.mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构检查完成")

def check_environment():
    """检查 .env 文件（支持项目根目录和 only_profile 目录）"""
    project_root = Path(__file__).resolve().parent.parent
    candidate_paths = [project_root / ".env", Path(__file__).parent / ".env"]

    for path in candidate_paths:
        if path.exists():
            # 输出相对路径以便阅读
            try:
                rel_path = path.relative_to(project_root)
            except ValueError:
                rel_path = path
            print(f"✅ 检测到 .env 文件: {rel_path}")
            return True

    # 未找到 .env
    print("⚠️  未检测到 .env 文件，使用默认配置")
    print("   建议在项目根目录或 only_profile 目录创建 .env，并配置 API 密钥")
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CogBridges Reddit用户画像分析启动器')
    parser.add_argument('--host', default='127.0.0.1', help='主机地址')
    parser.add_argument('--port', type=int, default=5000, help='端口号')
    parser.add_argument('--debug', action='store_true', help='调试模式')
    parser.add_argument('--check-only', action='store_true', help='仅检查环境，不启动应用')
    parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'], help='日志级别（默认INFO）')
    
    args = parser.parse_args()
    
    log_level = getattr(logging, args.log_level.upper(), logging.INFO)
    logger = setup_logging(log_level)
    
    print("🚀 CogBridges Reddit用户画像分析启动器")
    print("=" * 50)
    
    print(f"🔧 日志级别：{args.log_level.upper()}")
    
    # 环境检查
    print("\n📋 环境检查：")
    
    check_python_version()
    
    if not check_dependencies():
        sys.exit(1)
    
    create_directories()
    
    if not check_environment():
        sys.exit(1)
    
    if not check_config():
        sys.exit(1)
    
    print("\n✨ 环境检查完成！")
    
    if args.check_only:
        print("\n✅ 环境检查模式，不启动应用")
        return
    
    # 检查端口是否可用
    if not check_port_available(args.host, args.port):
        print(f"\n❌ 端口 {args.port} 已被占用！")
        print(f"请检查是否已有应用在运行，或使用其他端口：")
        print(f"python start.py --port 5001")
        sys.exit(1)

    # 启动应用
    print("\n🌟 启动应用...")
    try:
        # 添加only_profile和resona目录到Python路径
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
        only_profile_dir = os.path.join(project_root, 'only_profile')
        resona_dir = os.path.join(project_root, 'resona')
        sys.path.insert(0, only_profile_dir)
        sys.path.insert(0, resona_dir)
        sys.path.insert(0, project_root)

        print("🔧 导入应用模块...")
        from app import RedditProfileApp

        print("🔧 创建应用实例...")
        app = RedditProfileApp()
        
        print("🔧 检查应用对象...")
        print(f"   - app对象: {app}")
        print(f"   - app.app对象: {app.app}")
        print(f"   - app.run方法: {app.run}")
        print(f"   - app.app.run方法: {getattr(app.app, 'run', 'NOT_FOUND')}")
        
        print(f"\n🎯 应用启动中...")
        print(f"   - 地址：http://{args.host}:{args.port}")
        print(f"   - 调试模式：{'开启' if args.debug else '关闭'}")
        print(f"   - 按 Ctrl+C 停止应用")
        print("=" * 50)
        
        app.run(host=args.host, port=args.port, debug=args.debug)
        
    except KeyboardInterrupt:
        print("\n\n👋 应用已停止")
    except Exception as e:
        logger.error(f"启动失败：{e}")
        print(f"\n❌ 启动失败：{e}")
        sys.exit(1)

if __name__ == '__main__':
    main() 