"""
CogBridges Search - 集成测试
测试完整的业务流程
"""

import unittest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services import GoogleSearchService, RedditService, DataService
from models.search_models import SearchQuery, SearchResult, GoogleSearchResult
from models.reddit_models import RedditPost, RedditComment, RedditUser, UserHistory
from config import config


class TestIntegration(unittest.TestCase):
    """集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        
        # 保存原始配置
        self.original_configs = {
            'DATA_DIR': config.DATA_DIR,
            'RESULTS_DIR': config.RESULTS_DIR,
            'LOGS_DIR': config.LOGS_DIR,
            'GOOGLE_SEARCH_API_KEY': config.GOOGLE_SEARCH_API_KEY,
            'GOOGLE_SEARCH_ENGINE_ID': config.GOOGLE_SEARCH_ENGINE_ID,
            'REDDIT_CLIENT_ID': config.REDDIT_CLIENT_ID,
            'REDDIT_CLIENT_SECRET': config.REDDIT_CLIENT_SECRET,
        }
        
        # 设置测试配置
        config.DATA_DIR = self.temp_path
        config.RESULTS_DIR = self.temp_path / "results"
        config.LOGS_DIR = self.temp_path / "logs"
        config.GOOGLE_SEARCH_API_KEY = "test_google_key"
        config.GOOGLE_SEARCH_ENGINE_ID = "test_engine_id"
        config.REDDIT_CLIENT_ID = "test_reddit_id"
        config.REDDIT_CLIENT_SECRET = "test_reddit_secret"
    
    def tearDown(self):
        """测试后清理"""
        # 恢复原始配置
        for key, value in self.original_configs.items():
            setattr(config, key, value)
        
        # 删除临时目录
        shutil.rmtree(self.temp_dir)
    
    @patch('services.google_search.build')
    @patch('services.reddit_service.praw.Reddit')
    @patch('services.reddit_service.setup_proxy')
    def test_complete_search_workflow(self, mock_setup_proxy, mock_praw, mock_build):
        """测试完整的搜索工作流程"""
        # 1. 模拟Google搜索API响应
        mock_google_response = {
            'searchInformation': {'totalResults': '2'},
            'items': [
                {
                    'title': 'Python Programming Tips',
                    'link': 'https://reddit.com/r/Python/comments/abc123/python_tips/',
                    'snippet': 'Great tips for Python programming',
                    'displayLink': 'reddit.com'
                },
                {
                    'title': 'Advanced Python Techniques',
                    'link': 'https://reddit.com/r/Python/comments/def456/advanced_python/',
                    'snippet': 'Advanced techniques for Python developers',
                    'displayLink': 'reddit.com'
                }
            ]
        }
        
        mock_service = Mock()
        mock_cse = Mock()
        mock_list = Mock()
        mock_service.cse.return_value = mock_cse
        mock_cse.list.return_value = mock_list
        mock_list.execute.return_value = mock_google_response
        mock_build.return_value = mock_service
        
        # 2. 模拟Reddit API响应
        mock_setup_proxy.return_value = None
        mock_praw.return_value = Mock()
        
        # 模拟Reddit帖子
        mock_post1 = Mock()
        mock_post1.id = "abc123"
        mock_post1.title = "Python Programming Tips"
        mock_post1.selftext = "Here are some great Python tips..."
        mock_post1.author.name = "python_expert"
        mock_post1.score = 150
        mock_post1.upvote_ratio = 0.95
        mock_post1.num_comments = 25
        mock_post1.created_utc = 1640995200
        mock_post1.subreddit.display_name = "Python"
        mock_post1.permalink = "/r/Python/comments/abc123/python_tips/"
        mock_post1.url = "https://reddit.com/r/Python/comments/abc123/"
        mock_post1.is_self = True
        mock_post1.pinned = False
        mock_post1.locked = False
        mock_post1.archived = False
        mock_post1.over_18 = False
        
        # 模拟评论
        mock_comment1 = Mock()
        mock_comment1.id = "comment1"
        mock_comment1.body = "Great tips! Thanks for sharing."
        mock_comment1.author.name = "grateful_user"
        mock_comment1.score = 20
        mock_comment1.created_utc = 1640995300
        mock_comment1.parent_id = "t3_abc123"
        mock_comment1.subreddit.display_name = "Python"
        mock_comment1.permalink = "/r/Python/comments/abc123/python_tips/comment1/"
        mock_comment1.is_submitter = False
        mock_comment1.replies = []
        
        mock_comment2 = Mock()
        mock_comment2.id = "comment2"
        mock_comment2.body = "I've been using these techniques for years."
        mock_comment2.author.name = "experienced_dev"
        mock_comment2.score = 15
        mock_comment2.created_utc = 1640995400
        mock_comment2.parent_id = "t3_abc123"
        mock_comment2.subreddit.display_name = "Python"
        mock_comment2.permalink = "/r/Python/comments/abc123/python_tips/comment2/"
        mock_comment2.is_submitter = False
        mock_comment2.replies = []
        
        # 模拟用户信息
        mock_user1 = Mock()
        mock_user1.name = "grateful_user"
        mock_user1.created_utc = 1600000000
        mock_user1.comment_karma = 500
        mock_user1.link_karma = 100
        
        mock_user2 = Mock()
        mock_user2.name = "experienced_dev"
        mock_user2.created_utc = 1500000000
        mock_user2.comment_karma = 2000
        mock_user2.link_karma = 800
        
        # 3. 初始化服务
        google_service = GoogleSearchService()
        reddit_service = RedditService()
        data_service = DataService()
        
        # 4. 执行搜索
        search_result = google_service.search("python programming tips", max_results=2)
        
        # 验证搜索结果
        self.assertTrue(search_result.success)
        self.assertEqual(len(search_result.results), 2)
        self.assertEqual(search_result.results[0].title, "Python Programming Tips")
        
        # 获取Reddit帖子URL
        post_urls = search_result.get_post_urls()
        self.assertEqual(len(post_urls), 2)
        
        # 5. 模拟Reddit数据获取（由于异步复杂性，这里简化测试）
        # 在实际集成测试中，这里会调用reddit_service.process_search_results()
        
        # 6. 保存数据
        session_id = data_service.generate_session_id("python programming tips")
        
        # 保存搜索结果
        search_filepath = data_service.save_search_result(search_result, session_id)
        self.assertTrue(os.path.exists(search_filepath))
        
        # 模拟Reddit数据
        mock_reddit_data = {
            'posts_data': [
                {
                    'post': {
                        'id': 'abc123',
                        'title': 'Python Programming Tips',
                        'author': 'python_expert',
                        'score': 150,
                        'subreddit': 'Python'
                    },
                    'comments': [
                        {
                            'id': 'comment1',
                            'body': 'Great tips! Thanks for sharing.',
                            'author': 'grateful_user',
                            'score': 20
                        }
                    ]
                }
            ],
            'user_histories': {
                'grateful_user': {
                    'Python': {
                        'user': {
                            'username': 'grateful_user',
                            'comment_karma': 500,
                            'link_karma': 100
                        },
                        'posts': [],
                        'comments': [],
                        'statistics': {
                            'total_posts': 0,
                            'total_comments': 5,
                            'average_comment_score': 12.5
                        }
                    }
                }
            },
            'statistics': {
                'total_posts': 1,
                'total_commenters': 1,
                'processing_time': 3.5,
                'subreddits': ['Python']
            },
            'success': True
        }
        
        # 保存Reddit数据
        reddit_filepath = data_service.save_reddit_data(mock_reddit_data, session_id)
        self.assertTrue(os.path.exists(reddit_filepath))
        
        # 保存完整会话
        complete_filepath = data_service.save_complete_session(
            session_id, search_result, mock_reddit_data
        )
        self.assertTrue(os.path.exists(complete_filepath))
        
        # 7. 验证数据完整性
        loaded_session = data_service.load_session_data(session_id)
        self.assertIsNotNone(loaded_session)
        self.assertEqual(loaded_session['session_id'], session_id)
        self.assertEqual(
            loaded_session['search_result']['query']['query'], 
            "python programming tips"
        )
        self.assertEqual(len(loaded_session['reddit_data']['posts_data']), 1)
        self.assertEqual(
            loaded_session['reddit_data']['statistics']['total_posts'], 1
        )
        
        # 8. 测试会话列表
        sessions = data_service.list_sessions(limit=10)
        self.assertGreaterEqual(len(sessions), 1)
        
        session_found = False
        for session in sessions:
            if session['session_id'] == session_id:
                session_found = True
                self.assertEqual(session['query'], "python programming tips")
                break
        self.assertTrue(session_found)
        
        # 9. 测试统计信息
        google_stats = google_service.get_statistics()
        self.assertGreater(google_stats['request_count'], 0)
        self.assertGreater(google_stats['total_search_time'], 0)
        
        reddit_stats = reddit_service.get_statistics()
        self.assertIn('api_configured', reddit_stats)
        self.assertTrue(reddit_stats['api_configured'])
        
        storage_stats = data_service.get_storage_statistics()
        self.assertGreater(storage_stats['total_files'], 0)
        self.assertGreater(storage_stats['session_count'], 0)
    
    def test_error_handling_workflow(self):
        """测试错误处理工作流程"""
        # 测试配置错误
        config.GOOGLE_SEARCH_API_KEY = ""
        config.GOOGLE_SEARCH_ENGINE_ID = ""
        
        with self.assertRaises(ValueError):
            GoogleSearchService()
        
        config.REDDIT_CLIENT_ID = ""
        config.REDDIT_CLIENT_SECRET = ""
        
        with self.assertRaises(ValueError):
            RedditService()
    
    def test_data_consistency(self):
        """测试数据一致性"""
        # 创建数据服务
        data_service = DataService()
        
        # 创建测试数据
        query = SearchQuery(query="consistency test")
        search_result = SearchResult(query=query, success=True)
        reddit_data = {
            'posts_data': [],
            'user_histories': {},
            'statistics': {'total_posts': 0},
            'success': True
        }
        
        session_id = "consistency_test_session"
        
        # 保存数据
        data_service.save_complete_session(session_id, search_result, reddit_data)
        
        # 加载数据
        loaded_data = data_service.load_session_data(session_id)
        
        # 验证数据一致性
        self.assertEqual(loaded_data['session_id'], session_id)
        self.assertEqual(
            loaded_data['search_result']['query']['query'], 
            "consistency test"
        )
        self.assertTrue(loaded_data['reddit_data']['success'])
        
        # 删除数据
        success = data_service.delete_session(session_id)
        self.assertTrue(success)
        
        # 验证数据已删除
        loaded_data = data_service.load_session_data(session_id)
        self.assertIsNone(loaded_data)


if __name__ == '__main__':
    unittest.main()
