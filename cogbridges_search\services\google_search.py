"""
CogBridges Search - Google搜索服务
实现Google Custom Search API调用，支持Reddit内容搜索
"""

import time
from typing import List, Optional, Dict, Any
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import requests
from tenacity import retry, stop_after_attempt, wait_exponential

from ..config import config
from ..models.search_models import SearchQuery, SearchResult, GoogleSearchResult
from ..utils.logger_utils import get_logger, log_api_call, log_search_operation
from ..utils.proxy_utils import get_proxy_session


class GoogleSearchService:
    """Google搜索服务类"""
    
    def __init__(self):
        """初始化Google搜索服务"""
        self.logger = get_logger(__name__)
        self.api_key = config.GOOGLE_SEARCH_API_KEY
        self.search_engine_id = config.GOOGLE_SEARCH_ENGINE_ID
        
        # 验证配置
        if not config.google_search_configured:
            self.logger.error("Google搜索API未配置")
            raise ValueError("Google搜索API配置不完整")
        
        # 初始化搜索服务
        try:
            self.service = build("customsearch", "v1", developerKey=self.api_key)
            self.logger.info("Google搜索服务初始化成功")
        except Exception as e:
            self.logger.error(f"Google搜索服务初始化失败: {e}")
            raise
        
        # 请求统计
        self.request_count = 0
        self.total_search_time = 0.0
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    @log_api_call("Google", "customsearch/v1", "GET")
    def search(
        self,
        query: str,
        max_results: int = None,
        site_filter: str = "site:reddit.com",
        **kwargs
    ) -> SearchResult:
        """
        执行Google搜索
        
        Args:
            query: 搜索查询
            max_results: 最大结果数量
            site_filter: 站点过滤器
            **kwargs: 其他搜索参数
            
        Returns:
            搜索结果对象
        """
        start_time = time.time()
        
        # 创建搜索查询对象
        search_query = SearchQuery(
            query=query,
            max_results=max_results or config.GOOGLE_SEARCH_RESULTS_COUNT,
            site_filter=site_filter
        )
        
        self.logger.info(f"开始Google搜索: {query}")
        
        try:
            # 构建搜索查询字符串
            if site_filter:
                full_query = f"{query} {site_filter}"
            else:
                full_query = query
            
            # 执行搜索
            search_params = {
                'q': full_query,
                'cx': self.search_engine_id,
                'num': search_query.max_results,
                'start': kwargs.get('start', 1),
                'lr': kwargs.get('lr', 'lang_en|lang_zh'),  # 支持英文和中文
                'safe': kwargs.get('safe', 'medium'),
                'dateRestrict': kwargs.get('dateRestrict', 'm6'),  # 最近6个月
            }
            
            self.logger.debug(f"搜索参数: {search_params}")
            
            # 调用Google API
            result = self.service.cse().list(**search_params).execute()
            
            # 解析搜索结果
            search_results = self._parse_search_results(result)
            
            # 计算搜索时间
            search_time = time.time() - start_time
            
            # 创建搜索结果对象
            search_result = SearchResult(
                query=search_query,
                results=search_results,
                total_results=int(result.get('searchInformation', {}).get('totalResults', 0)),
                search_time=search_time,
                success=True
            )
            
            # 更新统计信息
            self.request_count += 1
            self.total_search_time += search_time
            
            # 记录搜索操作
            log_search_operation(query, len(search_results), search_time)
            
            self.logger.info(f"搜索完成: 找到 {len(search_results)} 个结果，耗时 {search_time:.2f}秒")
            
            return search_result
            
        except HttpError as e:
            error_msg = f"Google API错误: {e}"
            self.logger.error(error_msg)
            
            return SearchResult(
                query=search_query,
                success=False,
                error_message=error_msg,
                search_time=time.time() - start_time
            )
            
        except Exception as e:
            error_msg = f"搜索异常: {e}"
            self.logger.error(error_msg)
            
            return SearchResult(
                query=search_query,
                success=False,
                error_message=error_msg,
                search_time=time.time() - start_time
            )
    
    def _parse_search_results(self, api_result: Dict[str, Any]) -> List[GoogleSearchResult]:
        """
        解析Google API返回的搜索结果
        
        Args:
            api_result: Google API返回的原始结果
            
        Returns:
            解析后的搜索结果列表
        """
        results = []
        items = api_result.get('items', [])
        
        for i, item in enumerate(items):
            try:
                result = GoogleSearchResult(
                    title=item.get('title', ''),
                    url=item.get('link', ''),
                    snippet=item.get('snippet', ''),
                    display_url=item.get('displayLink', ''),
                    rank=i + 1
                )
                results.append(result)
                
            except Exception as e:
                self.logger.warning(f"解析搜索结果项失败: {e}")
                continue
        
        return results
    
    def search_reddit_posts(
        self,
        query: str,
        max_results: int = None,
        subreddit: str = None,
        time_filter: str = "m6"
    ) -> SearchResult:
        """
        专门搜索Reddit帖子
        
        Args:
            query: 搜索查询
            max_results: 最大结果数量
            subreddit: 指定子版块
            time_filter: 时间过滤器
            
        Returns:
            搜索结果对象
        """
        # 构建Reddit特定的搜索查询
        if subreddit:
            site_filter = f"site:reddit.com/r/{subreddit}"
        else:
            site_filter = "site:reddit.com"
        
        # 添加Reddit帖子特定的关键词
        reddit_query = f"{query} inurl:comments"
        
        return self.search(
            query=reddit_query,
            max_results=max_results,
            site_filter=site_filter,
            dateRestrict=time_filter
        )
    
    def get_search_suggestions(self, query: str) -> List[str]:
        """
        获取搜索建议（使用Google Suggest API）
        
        Args:
            query: 搜索查询
            
        Returns:
            搜索建议列表
        """
        try:
            session = get_proxy_session()
            
            # Google Suggest API
            suggest_url = "http://suggestqueries.google.com/complete/search"
            params = {
                'client': 'firefox',
                'q': query,
                'hl': 'en'
            }
            
            response = session.get(suggest_url, params=params, timeout=5)
            
            if response.status_code == 200:
                # 解析JSON响应
                suggestions_data = response.json()
                if len(suggestions_data) > 1:
                    return suggestions_data[1][:5]  # 返回前5个建议
            
            return []
            
        except Exception as e:
            self.logger.warning(f"获取搜索建议失败: {e}")
            return []
    
    def test_api_connection(self) -> bool:
        """
        测试Google API连接
        
        Returns:
            连接是否成功
        """
        try:
            # 执行简单的测试搜索
            test_result = self.search("test", max_results=1)
            
            if test_result.success:
                self.logger.info("Google API连接测试成功")
                return True
            else:
                self.logger.error(f"Google API连接测试失败: {test_result.error_message}")
                return False
                
        except Exception as e:
            self.logger.error(f"Google API连接测试异常: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取服务统计信息
        
        Returns:
            统计信息字典
        """
        avg_search_time = (
            self.total_search_time / self.request_count 
            if self.request_count > 0 else 0
        )
        
        return {
            "request_count": self.request_count,
            "total_search_time": self.total_search_time,
            "average_search_time": avg_search_time,
            "api_configured": config.google_search_configured,
            "max_results_per_search": config.GOOGLE_SEARCH_RESULTS_COUNT
        }


if __name__ == "__main__":
    # 测试Google搜索服务
    print("CogBridges Search - Google搜索服务测试")
    print("=" * 50)
    
    try:
        # 创建搜索服务
        search_service = GoogleSearchService()
        
        # 测试API连接
        print("🔗 测试API连接...")
        if search_service.test_api_connection():
            print("✅ API连接正常")
        else:
            print("❌ API连接失败")
            exit(1)
        
        # 执行测试搜索
        print("\n🔍 执行测试搜索...")
        test_query = "python programming reddit"
        result = search_service.search_reddit_posts(test_query, max_results=3)
        
        if result.success:
            print(f"✅ 搜索成功: 找到 {len(result.results)} 个结果")
            
            for i, item in enumerate(result.results, 1):
                print(f"\n{i}. {item.title}")
                print(f"   URL: {item.url}")
                print(f"   摘要: {item.snippet[:100]}...")
        else:
            print(f"❌ 搜索失败: {result.error_message}")
        
        # 显示统计信息
        print("\n📊 服务统计:")
        stats = search_service.get_statistics()
        for key, value in stats.items():
            print(f"  {key}: {value}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
