"""
CogBridges Search - 搜索相关数据模型
定义搜索查询、搜索结果等数据结构
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from datetime import datetime
import json


@dataclass
class SearchQuery:
    """搜索查询模型"""
    query: str
    timestamp: datetime = field(default_factory=datetime.now)
    search_type: str = "reddit"  # 搜索类型：reddit, general
    max_results: int = 5
    site_filter: str = "site:reddit.com"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "query": self.query,
            "timestamp": self.timestamp.isoformat(),
            "search_type": self.search_type,
            "max_results": self.max_results,
            "site_filter": self.site_filter
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchQuery':
        """从字典创建实例"""
        data = data.copy()
        if 'timestamp' in data:
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)


@dataclass
class GoogleSearchResult:
    """Google搜索结果项"""
    title: str
    url: str
    snippet: str
    display_url: str = ""
    rank: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "title": self.title,
            "url": self.url,
            "snippet": self.snippet,
            "display_url": self.display_url,
            "rank": self.rank
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GoogleSearchResult':
        """从字典创建实例"""
        return cls(**data)
    
    @property
    def is_reddit_url(self) -> bool:
        """检查是否为Reddit URL"""
        return "reddit.com" in self.url.lower()
    
    def extract_reddit_info(self) -> Optional[Dict[str, str]]:
        """提取Reddit URL信息"""
        if not self.is_reddit_url:
            return None
        
        # 解析Reddit URL格式
        # 例如: https://www.reddit.com/r/subreddit/comments/post_id/title/
        import re
        
        # 匹配Reddit帖子URL
        post_pattern = r'reddit\.com/r/([^/]+)/comments/([^/]+)'
        match = re.search(post_pattern, self.url)
        
        if match:
            return {
                "type": "post",
                "subreddit": match.group(1),
                "post_id": match.group(2),
                "url": self.url
            }
        
        # 匹配Reddit用户URL
        user_pattern = r'reddit\.com/u(?:ser)?/([^/]+)'
        match = re.search(user_pattern, self.url)
        
        if match:
            return {
                "type": "user",
                "username": match.group(1),
                "url": self.url
            }
        
        # 匹配子版块URL
        subreddit_pattern = r'reddit\.com/r/([^/]+)/?$'
        match = re.search(subreddit_pattern, self.url)
        
        if match:
            return {
                "type": "subreddit",
                "subreddit": match.group(1),
                "url": self.url
            }
        
        return {
            "type": "unknown",
            "url": self.url
        }


@dataclass
class SearchResult:
    """完整搜索结果"""
    query: SearchQuery
    results: List[GoogleSearchResult] = field(default_factory=list)
    total_results: int = 0
    search_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    success: bool = True
    error_message: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "query": self.query.to_dict(),
            "results": [result.to_dict() for result in self.results],
            "total_results": self.total_results,
            "search_time": self.search_time,
            "timestamp": self.timestamp.isoformat(),
            "success": self.success,
            "error_message": self.error_message
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchResult':
        """从字典创建实例"""
        data = data.copy()
        
        # 转换嵌套对象
        if 'query' in data:
            data['query'] = SearchQuery.from_dict(data['query'])
        
        if 'results' in data:
            data['results'] = [GoogleSearchResult.from_dict(r) for r in data['results']]
        
        if 'timestamp' in data:
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        
        return cls(**data)
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'SearchResult':
        """从JSON字符串创建实例"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def get_reddit_results(self) -> List[GoogleSearchResult]:
        """获取Reddit相关的搜索结果"""
        return [result for result in self.results if result.is_reddit_url]
    
    def get_post_urls(self) -> List[str]:
        """获取Reddit帖子URL列表"""
        post_urls = []
        for result in self.get_reddit_results():
            reddit_info = result.extract_reddit_info()
            if reddit_info and reddit_info.get("type") == "post":
                post_urls.append(result.url)
        return post_urls
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取搜索结果统计信息"""
        reddit_results = self.get_reddit_results()
        post_urls = self.get_post_urls()
        
        # 统计子版块分布
        subreddits = {}
        for result in reddit_results:
            reddit_info = result.extract_reddit_info()
            if reddit_info and "subreddit" in reddit_info:
                subreddit = reddit_info["subreddit"]
                subreddits[subreddit] = subreddits.get(subreddit, 0) + 1
        
        return {
            "total_results": len(self.results),
            "reddit_results": len(reddit_results),
            "post_urls": len(post_urls),
            "subreddit_distribution": subreddits,
            "search_time": self.search_time,
            "success": self.success
        }
