2025-07-07 17:57:31,136 - url_parser - INFO - Reddit链接解析器初始化完成
2025-07-07 17:57:31,710 - __main__ - ERROR - 启动失败：7 validation errors for Settings
http_proxy
  Extra inputs are not permitted [type=extra_forbidden, input_value='http://127.0.0.1:7890', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
https_proxy
  Extra inputs are not permitted [type=extra_forbidden, input_value='http://127.0.0.1:7890', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
max_posts_per_user
  Extra inputs are not permitted [type=extra_forbidden, input_value='200', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
max_comments_per_user
  Extra inputs are not permitted [type=extra_forbidden, input_value='300', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
analysis_timeout
  Extra inputs are not permitted [type=extra_forbidden, input_value='300', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
cache_expiry_hours
  Extra inputs are not permitted [type=extra_forbidden, input_value='24', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
replicate_api_token
  Extra inputs are not permitted [type=extra_forbidden, input_value='****************************************', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
2025-07-08 23:28:25,064 - url_parser - INFO - Reddit链接解析器初始化完成
2025-07-08 23:28:25,341 - __main__ - ERROR - 启动失败：'NoneType' object is not callable
2025-07-08 23:29:11,207 - url_parser - INFO - Reddit链接解析器初始化完成
2025-07-08 23:29:11,504 - __main__ - ERROR - 启动失败：'NoneType' object is not callable
