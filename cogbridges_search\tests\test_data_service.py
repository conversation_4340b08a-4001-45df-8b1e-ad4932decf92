"""
CogBridges Search - 数据服务单元测试
"""

import unittest
import tempfile
import shutil
import json
import os
from pathlib import Path
from datetime import datetime
import sys

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.data_service import DataService
from models.search_models import SearchQuery, SearchResult, GoogleSearchResult
from models.reddit_models import RedditPost, RedditComment, RedditUser, UserHistory
from config import config


class TestDataService(unittest.TestCase):
    """数据服务测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        
        # 保存原始配置
        self.original_data_dir = config.DATA_DIR
        self.original_results_dir = config.RESULTS_DIR
        self.original_logs_dir = config.LOGS_DIR
        
        # 设置测试配置
        config.DATA_DIR = self.temp_path
        config.RESULTS_DIR = self.temp_path / "results"
        config.LOGS_DIR = self.temp_path / "logs"
        
        # 创建数据服务实例
        self.data_service = DataService()
    
    def tearDown(self):
        """测试后清理"""
        # 恢复原始配置
        config.DATA_DIR = self.original_data_dir
        config.RESULTS_DIR = self.original_results_dir
        config.LOGS_DIR = self.original_logs_dir
        
        # 删除临时目录
        shutil.rmtree(self.temp_dir)
    
    def test_service_initialization(self):
        """测试服务初始化"""
        self.assertIsNotNone(self.data_service)
        self.assertTrue(config.DATA_DIR.exists())
        self.assertTrue(config.RESULTS_DIR.exists())
        self.assertTrue(config.LOGS_DIR.exists())
    
    def test_generate_session_id(self):
        """测试会话ID生成"""
        query = "test query"
        session_id = self.data_service.generate_session_id(query)
        
        self.assertIsInstance(session_id, str)
        self.assertGreater(len(session_id), 10)
        self.assertIn("_", session_id)
        
        # 测试相同查询生成不同ID（因为时间戳不同）
        import time
        time.sleep(1)
        session_id2 = self.data_service.generate_session_id(query)
        self.assertNotEqual(session_id, session_id2)
    
    def test_save_search_result(self):
        """测试保存搜索结果"""
        # 创建测试搜索结果
        query = SearchQuery(query="test query")
        results = [
            GoogleSearchResult(
                title="Test Result",
                url="https://reddit.com/r/test/comments/123/",
                snippet="Test snippet",
                rank=1
            )
        ]
        search_result = SearchResult(
            query=query,
            results=results,
            total_results=1,
            success=True
        )
        
        session_id = "test_session_123"
        filepath = self.data_service.save_search_result(search_result, session_id)
        
        # 验证文件存在
        self.assertTrue(os.path.exists(filepath))
        
        # 验证文件内容
        with open(filepath, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        self.assertEqual(saved_data['query']['query'], "test query")
        self.assertEqual(len(saved_data['results']), 1)
        self.assertEqual(saved_data['results'][0]['title'], "Test Result")
        self.assertTrue(saved_data['success'])
    
    def test_save_reddit_data(self):
        """测试保存Reddit数据"""
        # 创建测试Reddit数据
        reddit_data = {
            'posts_data': [
                {
                    'post': {
                        'id': 'test123',
                        'title': 'Test Post',
                        'author': 'testuser',
                        'score': 100
                    },
                    'comments': [
                        {
                            'id': 'comment1',
                            'body': 'Test comment',
                            'author': 'commenter1',
                            'score': 25
                        }
                    ]
                }
            ],
            'user_histories': {},
            'statistics': {
                'total_posts': 1,
                'total_commenters': 1,
                'processing_time': 5.5
            },
            'success': True
        }
        
        session_id = "test_session_456"
        filepath = self.data_service.save_reddit_data(reddit_data, session_id)
        
        # 验证文件存在
        self.assertTrue(os.path.exists(filepath))
        
        # 验证文件内容
        with open(filepath, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        self.assertEqual(len(saved_data['posts_data']), 1)
        self.assertEqual(saved_data['posts_data'][0]['post']['title'], 'Test Post')
        self.assertTrue(saved_data['success'])
    
    def test_save_complete_session(self):
        """测试保存完整会话数据"""
        # 创建测试数据
        query = SearchQuery(query="complete test")
        search_result = SearchResult(query=query, success=True)
        reddit_data = {
            'posts_data': [],
            'user_histories': {},
            'statistics': {'total_posts': 0, 'processing_time': 1.0},
            'success': True
        }
        metadata = {'test_mode': True}
        
        session_id = "complete_session_789"
        filepath = self.data_service.save_complete_session(
            session_id, search_result, reddit_data, metadata
        )
        
        # 验证文件存在
        self.assertTrue(os.path.exists(filepath))
        
        # 验证文件内容
        with open(filepath, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        self.assertEqual(saved_data['session_id'], session_id)
        self.assertIn('timestamp', saved_data)
        self.assertIn('search_result', saved_data)
        self.assertIn('reddit_data', saved_data)
        self.assertIn('metadata', saved_data)
        self.assertIn('statistics', saved_data)
        self.assertTrue(saved_data['metadata']['test_mode'])
    
    def test_load_session_data(self):
        """测试加载会话数据"""
        # 先保存一个会话
        query = SearchQuery(query="load test")
        search_result = SearchResult(query=query, success=True)
        reddit_data = {'success': True, 'posts_data': []}
        
        session_id = "load_test_session"
        self.data_service.save_complete_session(session_id, search_result, reddit_data)
        
        # 加载会话数据
        loaded_data = self.data_service.load_session_data(session_id)
        
        self.assertIsNotNone(loaded_data)
        self.assertEqual(loaded_data['session_id'], session_id)
        self.assertEqual(loaded_data['search_result']['query']['query'], "load test")
    
    def test_load_nonexistent_session(self):
        """测试加载不存在的会话"""
        result = self.data_service.load_session_data("nonexistent_session")
        self.assertIsNone(result)
    
    def test_list_sessions(self):
        """测试列出会话"""
        # 创建多个测试会话
        sessions_to_create = ["session1", "session2", "session3"]
        
        for session_id in sessions_to_create:
            query = SearchQuery(query=f"test {session_id}")
            search_result = SearchResult(query=query, success=True)
            reddit_data = {'success': True, 'posts_data': []}
            self.data_service.save_complete_session(session_id, search_result, reddit_data)
        
        # 列出会话
        sessions = self.data_service.list_sessions(limit=10)
        
        self.assertGreaterEqual(len(sessions), 3)
        
        # 验证会话信息结构
        for session in sessions:
            self.assertIn('session_id', session)
            self.assertIn('timestamp', session)
            self.assertIn('query', session)
            self.assertIn('file_size', session)
            self.assertIn('statistics', session)
    
    def test_delete_session(self):
        """测试删除会话"""
        # 创建测试会话
        query = SearchQuery(query="delete test")
        search_result = SearchResult(query=query, success=True)
        reddit_data = {'success': True, 'posts_data': []}
        
        session_id = "delete_test_session"
        filepath = self.data_service.save_complete_session(session_id, search_result, reddit_data)
        
        # 验证文件存在
        self.assertTrue(os.path.exists(filepath))
        
        # 删除会话
        result = self.data_service.delete_session(session_id)
        self.assertTrue(result)
        
        # 验证文件已删除
        self.assertFalse(os.path.exists(filepath))
        
        # 再次删除应该返回False
        result = self.data_service.delete_session(session_id)
        self.assertFalse(result)
    
    def test_make_serializable(self):
        """测试数据序列化"""
        # 创建包含各种数据类型的测试数据
        test_data = {
            'string': 'test',
            'number': 123,
            'boolean': True,
            'datetime': datetime.now(),
            'list': [1, 2, 3],
            'nested_dict': {
                'inner_string': 'inner_test',
                'inner_datetime': datetime.now()
            }
        }
        
        # 添加一个有to_dict方法的对象
        mock_object = type('MockObject', (), {
            'to_dict': lambda self: {'mock': 'data'}
        })()
        test_data['mock_object'] = mock_object
        
        serializable_data = self.data_service._make_serializable(test_data)
        
        # 验证结果可以JSON序列化
        json_str = json.dumps(serializable_data)
        self.assertIsInstance(json_str, str)
        
        # 验证datetime被转换为ISO格式字符串
        self.assertIsInstance(serializable_data['datetime'], str)
        self.assertIsInstance(serializable_data['nested_dict']['inner_datetime'], str)
        
        # 验证mock对象被转换
        self.assertEqual(serializable_data['mock_object'], {'mock': 'data'})
    
    def test_get_storage_statistics(self):
        """测试获取存储统计"""
        # 创建一些测试文件
        for i in range(3):
            query = SearchQuery(query=f"stats test {i}")
            search_result = SearchResult(query=query, success=True)
            reddit_data = {'success': True, 'posts_data': []}
            self.data_service.save_complete_session(f"stats_session_{i}", search_result, reddit_data)
        
        stats = self.data_service.get_storage_statistics()
        
        self.assertIn('total_files', stats)
        self.assertIn('total_size_bytes', stats)
        self.assertIn('total_size_mb', stats)
        self.assertIn('session_count', stats)
        self.assertIn('data_directory', stats)
        self.assertIn('results_directory', stats)
        self.assertIn('logs_directory', stats)
        
        self.assertGreaterEqual(stats['total_files'], 3)
        self.assertGreaterEqual(stats['session_count'], 3)
        self.assertGreater(stats['total_size_bytes'], 0)


if __name__ == '__main__':
    unittest.main()
