#!/usr/bin/env python3
"""
CogBridges Search - 测试运行脚本
运行所有单元测试和集成测试
"""

import sys
import os
import unittest
import argparse
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入配置验证
from config import validate_startup_config


def discover_tests(test_dir="tests", pattern="test_*.py"):
    """
    发现测试文件
    
    Args:
        test_dir: 测试目录
        pattern: 测试文件模式
        
    Returns:
        测试套件
    """
    loader = unittest.TestLoader()
    start_dir = project_root / test_dir
    
    if not start_dir.exists():
        print(f"❌ 测试目录不存在: {start_dir}")
        return None
    
    suite = loader.discover(str(start_dir), pattern=pattern)
    return suite


def run_tests(test_suite, verbosity=2):
    """
    运行测试套件
    
    Args:
        test_suite: 测试套件
        verbosity: 详细程度
        
    Returns:
        测试结果
    """
    runner = unittest.TextTestRunner(
        verbosity=verbosity,
        stream=sys.stdout,
        buffer=True
    )
    
    result = runner.run(test_suite)
    return result


def run_specific_test(test_module, test_class=None, test_method=None, verbosity=2):
    """
    运行特定测试
    
    Args:
        test_module: 测试模块名
        test_class: 测试类名（可选）
        test_method: 测试方法名（可选）
        verbosity: 详细程度
        
    Returns:
        测试结果
    """
    try:
        # 构建测试名称
        if test_class and test_method:
            test_name = f"tests.{test_module}.{test_class}.{test_method}"
        elif test_class:
            test_name = f"tests.{test_module}.{test_class}"
        else:
            test_name = f"tests.{test_module}"
        
        # 加载测试
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromName(test_name)
        
        # 运行测试
        return run_tests(suite, verbosity)
        
    except Exception as e:
        print(f"❌ 加载测试失败: {e}")
        return None


def print_test_summary(result):
    """
    打印测试摘要
    
    Args:
        result: 测试结果
    """
    print("\n" + "=" * 70)
    print("📊 测试摘要")
    print("=" * 70)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(result.skipped) if hasattr(result, 'skipped') else 0
    success = total_tests - failures - errors - skipped
    
    print(f"总测试数: {total_tests}")
    print(f"✅ 成功: {success}")
    print(f"❌ 失败: {failures}")
    print(f"💥 错误: {errors}")
    print(f"⏭️ 跳过: {skipped}")
    
    success_rate = (success / total_tests * 100) if total_tests > 0 else 0
    print(f"成功率: {success_rate:.1f}%")
    
    if result.wasSuccessful():
        print("\n🎉 所有测试通过！")
    else:
        print("\n⚠️ 存在测试失败或错误")
        
        if result.failures:
            print(f"\n❌ 失败的测试 ({len(result.failures)}):")
            for test, traceback in result.failures:
                print(f"  - {test}")
        
        if result.errors:
            print(f"\n💥 错误的测试 ({len(result.errors)}):")
            for test, traceback in result.errors:
                print(f"  - {test}")


def check_test_environment():
    """
    检查测试环境
    
    Returns:
        环境是否正常
    """
    print("🔍 检查测试环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version < (3, 7):
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        print("   需要Python 3.7或更高版本")
        return False
    
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查必要的模块
    required_modules = [
        'unittest', 'unittest.mock', 'asyncio', 'json', 'pathlib'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少必要模块: {', '.join(missing_modules)}")
        return False
    
    print("✅ 必要模块检查通过")
    
    # 检查测试目录
    tests_dir = project_root / "tests"
    if not tests_dir.exists():
        print(f"❌ 测试目录不存在: {tests_dir}")
        return False
    
    test_files = list(tests_dir.glob("test_*.py"))
    if not test_files:
        print(f"❌ 未找到测试文件")
        return False
    
    print(f"✅ 找到 {len(test_files)} 个测试文件")
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="CogBridges Search 测试运行器")
    
    parser.add_argument(
        '--module', '-m',
        help='运行特定测试模块 (例如: test_google_search)'
    )
    
    parser.add_argument(
        '--class', '-c',
        dest='test_class',
        help='运行特定测试类 (例如: TestGoogleSearchService)'
    )
    
    parser.add_argument(
        '--method', '-t',
        dest='test_method',
        help='运行特定测试方法 (例如: test_search_success)'
    )
    
    parser.add_argument(
        '--verbosity', '-v',
        type=int,
        default=2,
        choices=[0, 1, 2],
        help='详细程度 (0=静默, 1=正常, 2=详细)'
    )
    
    parser.add_argument(
        '--no-env-check',
        action='store_true',
        help='跳过环境检查'
    )
    
    parser.add_argument(
        '--integration-only',
        action='store_true',
        help='仅运行集成测试'
    )
    
    parser.add_argument(
        '--unit-only',
        action='store_true',
        help='仅运行单元测试'
    )
    
    args = parser.parse_args()
    
    print("🚀 CogBridges Search - 测试运行器")
    print("=" * 50)
    
    # 环境检查
    if not args.no_env_check:
        if not check_test_environment():
            print("\n❌ 环境检查失败，测试终止")
            sys.exit(1)
        print()
    
    # 运行特定测试
    if args.module:
        print(f"🎯 运行特定测试: {args.module}")
        if args.test_class:
            print(f"   测试类: {args.test_class}")
        if args.test_method:
            print(f"   测试方法: {args.test_method}")
        print()
        
        result = run_specific_test(
            args.module, 
            args.test_class, 
            args.test_method, 
            args.verbosity
        )
        
        if result:
            print_test_summary(result)
            sys.exit(0 if result.wasSuccessful() else 1)
        else:
            sys.exit(1)
    
    # 运行所有测试或特定类型的测试
    test_patterns = []
    
    if args.integration_only:
        test_patterns = ["test_integration.py"]
        print("🔗 运行集成测试")
    elif args.unit_only:
        test_patterns = ["test_google_search.py", "test_reddit_service.py", "test_data_service.py"]
        print("🧪 运行单元测试")
    else:
        test_patterns = ["test_*.py"]
        print("🧪 运行所有测试")
    
    print()
    
    all_results = []
    
    for pattern in test_patterns:
        print(f"📋 发现测试: {pattern}")
        suite = discover_tests(pattern=pattern)
        
        if suite and suite.countTestCases() > 0:
            print(f"   找到 {suite.countTestCases()} 个测试")
            result = run_tests(suite, args.verbosity)
            all_results.append(result)
        else:
            print(f"   未找到匹配的测试")
    
    if not all_results:
        print("❌ 未运行任何测试")
        sys.exit(1)
    
    # 合并结果
    total_tests = sum(r.testsRun for r in all_results)
    total_failures = sum(len(r.failures) for r in all_results)
    total_errors = sum(len(r.errors) for r in all_results)
    all_successful = all(r.wasSuccessful() for r in all_results)
    
    # 创建合并的结果对象
    class CombinedResult:
        def __init__(self):
            self.testsRun = total_tests
            self.failures = []
            self.errors = []
            self.skipped = []
            
            for result in all_results:
                self.failures.extend(result.failures)
                self.errors.extend(result.errors)
                if hasattr(result, 'skipped'):
                    self.skipped.extend(result.skipped)
        
        def wasSuccessful(self):
            return len(self.failures) == 0 and len(self.errors) == 0
    
    combined_result = CombinedResult()
    print_test_summary(combined_result)
    
    sys.exit(0 if combined_result.wasSuccessful() else 1)


if __name__ == '__main__':
    main()
