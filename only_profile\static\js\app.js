/**
 * CogBridges Reddit用户画像分析 - 前端应用
 */

class RedditProfileAnalyzer {
    constructor() {
        this.network = null;
        this.nodes = null;
        this.edges = null;
        this.currentAnalysisData = null;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.initTooltips();

        // 强制显示结果区域进行测试
        setTimeout(() => {
            console.log('开始初始化测试...');

            // 先测试基本功能
            const testElement = document.getElementById('username-display');
            if (testElement) {
                testElement.textContent = 'JavaScript正常工作';
                console.log('JavaScript基本功能测试通过');
            } else {
                console.error('找不到username-display元素');
            }

            // 强制显示结果区域
            const resultsRow = document.getElementById('results-row');
            if (resultsRow) {
                resultsRow.style.display = 'block';
                console.log('强制显示结果区域');
            } else {
                console.error('找不到results-row元素');
            }

            this.loadLastResult();
        }, 1000);

        console.log('Reddit Profile Analyzer initialized');
    }
    
    bindEvents() {
        // 绑定分析按钮点击事件
        document.getElementById('analyze-btn').addEventListener('click', () => {
            this.startAnalysis();
        });
        
        // 绑定输入框回车事件
        document.getElementById('reddit-url').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.startAnalysis();
            }
        });
        
        // 绑定示例链接点击事件
        document.querySelectorAll('.example-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const url = link.getAttribute('data-url');
                document.getElementById('reddit-url').value = url;
            });
        });
        
        // 绑定图谱控制按钮
        document.getElementById('zoom-in-btn').addEventListener('click', () => {
            this.zoomIn();
        });
        
        document.getElementById('zoom-out-btn').addEventListener('click', () => {
            this.zoomOut();
        });
        
        document.getElementById('reset-view-btn').addEventListener('click', () => {
            this.resetView();
        });
        
        // 绑定评论分析按钮
        document.getElementById('analyze-comment-btn').addEventListener('click', () => {
            this.analyzeComment();
        });
        
        // 绑定评论输入框回车事件（Ctrl+Enter）
        document.getElementById('comment-input').addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                this.analyzeComment();
            }
        });
    }
    
    initTooltips() {
        // 初始化Bootstrap工具提示
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    async startAnalysis() {
        const url = document.getElementById('reddit-url').value.trim();
        
        if (!url) {
            this.showError('请输入Reddit链接');
            return;
        }
        
        if (!this.isValidRedditUrl(url)) {
            this.showError('请输入有效的Reddit链接');
            return;
        }
        
        this.showStatus('正在解析Reddit链接...', 'info');
        this.hideResults();
        
        try {
            // 调用后端API进行分析
            const result = await this.callAnalysisAPI(url);
            
            if (result.success) {
                this.currentAnalysisData = result;
                this.displayResults(result);
                this.hideStatus();
            } else {
                this.showError(result.error || '分析失败，请重试');
            }
        } catch (error) {
            console.error('Analysis error:', error);
            this.showError('网络错误或服务暂时不可用');
        }
    }
    
    isValidRedditUrl(url) {
        const redditUrlPattern = /^https?:\/\/(www\.)?(reddit\.com|old\.reddit\.com|m\.reddit\.com|redd\.it)/;
        return redditUrlPattern.test(url);
    }
    
    async callAnalysisAPI(url) {
        // 模拟API调用过程
        this.updateStatus('正在解析链接...');
        await this.delay(1000);
        
        this.updateStatus('正在抓取用户数据...');
        await this.delay(2000);
        
        this.updateStatus('正在进行语义分析...');
        await this.delay(2000);
        
        this.updateStatus('正在构建人格图谱...');
        await this.delay(2000);
        
        // 实际的API调用
        try {
            const response = await axios.post('/api/analyze', {
                url: url
            }, {
                timeout: 120000,  // 2分钟超时
                onUploadProgress: (progressEvent) => {
                    // 可以显示上传进度
                }
            });
            
            return response.data;
        } catch (error) {
            if (error.code === 'ECONNABORTED') {
                throw new Error('分析超时，请重试');
            }
            throw error;
        }
    }
    
    // 模拟数据，用于开发测试
    generateMockData() {
        return {
            success: true,
            username: "example_user",
            user_profile: {
                user_id: "example_user",
                graph: {
                    nodes: {
                        "exp_1": {
                            node_id: "exp_1",
                            node_type: "EXPERIENCE",
                            content: "在科技公司工作3年的经历",
                            weight: 0.8,
                            metadata: { source: "analysis" }
                        },
                        "belief_1": {
                            node_id: "belief_1",
                            node_type: "BELIEF",
                            content: "重视工作与生活平衡",
                            weight: 0.7,
                            metadata: { source: "analysis" }
                        },
                        "emotion_1": {
                            node_id: "emotion_1",
                            node_type: "EMOTION",
                            content: "对未来发展感到焦虑",
                            weight: 0.6,
                            metadata: { source: "analysis" }
                        },
                        "topic_1": {
                            node_id: "topic_1",
                            node_type: "TOPIC",
                            content: "职业发展规划",
                            weight: 0.9,
                            metadata: { source: "analysis" }
                        }
                    },
                    edges: {
                        "edge_1": {
                            source_id: "exp_1",
                            target_id: "belief_1",
                            relation_type: "LEADS_TO",
                            weight: 0.7,
                            evidence: "工作经历塑造了价值观"
                        },
                        "edge_2": {
                            source_id: "topic_1",
                            target_id: "emotion_1",
                            relation_type: "TRIGGERS",
                            weight: 0.6,
                            evidence: "职业话题引发焦虑情绪"
                        }
                    }
                },
                completeness_score: 0.75
            },
            graph_explanation: {
                overall_portrait: "这是一个关注职业发展、注重工作生活平衡的用户。在科技行业有一定经验，但对未来发展方向存在焦虑。",
                key_traits: ["职业导向", "理性思考", "适度焦虑"],
                main_insights: ["工作经验影响价值观形成", "职业话题是主要关注点", "存在发展焦虑需要关注"],
                interaction_recommendations: ["分享职业发展经验", "提供实用建议", "理解其焦虑情绪"]
            },
            quality_metrics: {
                node_count: 4,
                edge_count: 2,
                overall_quality: "medium",
                completeness_score: 0.75
            },
            content_stats: {
                total_posts: 15,
                total_comments: 42,
                processed_texts: 20,
                subreddit_count: 8
            }
        };
    }
    
    displayResults(data) {
        // 显示结果区域
        this.showResults();
        
        // 显示用户信息
        this.displayUserInfo(data);
        
        // 显示分析摘要
        this.displayAnalysisSummary(data);
        
        // 显示质量指标
        this.displayQualityMetrics(data);
        
        // 显示图谱
        this.displayGraph(data.user_profile.graph);
        
        // 显示详细分析
        this.displayDetailedAnalysis(data.graph_explanation, data);
        
        // 检查并显示目标内容分析
        this.displayTargetContentAnalysis(data);
        
        // 添加淡入动画
        document.getElementById('results-row').classList.add('fade-in');
    }
    
    displayUserInfo(data) {
        console.log('显示用户信息:', data);

        document.getElementById('username-display').textContent = data.username || 'Unknown';
        document.getElementById('post-count').textContent = data.content_stats?.posts || 0;
        document.getElementById('comment-count').textContent = data.content_stats?.comments || 0;
        document.getElementById('subreddit-count').textContent = data.content_stats?.subreddit_count || 0;

        const confidence = Math.round((data.user_profile?.completeness_score || 0) * 100);
        document.getElementById('confidence-score').textContent = `${confidence}%`;
    }
    
    displayAnalysisSummary(data) {
        // 显示情绪标签
        const emotionContainer = document.getElementById('emotion-tags');
        emotionContainer.innerHTML = '';

        // 从图谱节点中获取情绪数据
        const graphNodes = data.user_profile?.graph?.nodes || {};
        Object.values(graphNodes).forEach(node => {
            if (node.node_type === 'EMOTION') {
                const tag = this.createTag(node.content, 'emotion');
                emotionContainer.appendChild(tag);
            }
        });

        // 如果图谱中没有情绪节点，尝试从语义分析结果中获取
        if (emotionContainer.children.length === 0) {
            const emotionalState = data.semantic_stats?.parsed_query?.emotional_state || {};
            const dominantEmotions = Object.entries(emotionalState)
                .filter(([emotion, intensity]) => intensity > 0.3)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 3)
                .map(([emotion, intensity]) => emotion);

            dominantEmotions.forEach(emotion => {
                const tag = this.createTag(this.translateEmotion(emotion), 'emotion');
                emotionContainer.appendChild(tag);
            });
        }

        // 显示话题标签
        const topicContainer = document.getElementById('topic-tags');
        topicContainer.innerHTML = '';

        // 从图谱节点中获取话题数据
        Object.values(graphNodes).forEach(node => {
            if (node.node_type === 'INTEREST' || node.node_type === 'TOPIC') {
                const tag = this.createTag(node.content, 'topic');
                topicContainer.appendChild(tag);
            }
        });

        // 如果图谱中没有话题节点，尝试从语义分析结果中获取
        if (topicContainer.children.length === 0) {
            const topics = data.semantic_stats?.parsed_query?.topics || [];
            topics.slice(0, 5).forEach(topic => {
                const tag = this.createTag(topic, 'topic');
                topicContainer.appendChild(tag);
            });
        }

        // 显示性格特征标签
        const personalityContainer = document.getElementById('personality-tags');
        personalityContainer.innerHTML = '';

        // 从图谱解释中获取关键特征
        if (data.graph_explanation?.key_traits) {
            data.graph_explanation.key_traits.forEach(trait => {
                const tag = this.createTag(trait, 'personality');
                personalityContainer.appendChild(tag);
            });
        }

        // 如果没有关键特征，尝试从用户特征中获取
        if (personalityContainer.children.length === 0) {
            const personalityTraits = data.semantic_stats?.user_characteristics?.personality_traits || {};
            const topTraits = Object.entries(personalityTraits)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 3)
                .map(([trait, score]) => this.translatePersonalityTrait(trait));

            topTraits.forEach(trait => {
                const tag = this.createTag(trait, 'personality');
                personalityContainer.appendChild(tag);
            });
        }
    }
    
    createTag(text, type) {
        const tag = document.createElement('span');
        tag.className = `tag ${type}`;
        tag.textContent = text;
        return tag;
    }

    translateEmotion(emotion) {
        const emotionMap = {
            'joy': '快乐',
            'happiness': '幸福',
            'sadness': '悲伤',
            'anger': '愤怒',
            'fear': '恐惧',
            'anxiety': '焦虑',
            'excitement': '兴奋',
            'frustration': '沮丧',
            'confusion': '困惑',
            'hope': '希望',
            'disappointment': '失望',
            'pride': '自豪',
            'shame': '羞耻',
            'guilt': '内疚',
            'surprise': '惊讶',
            'disgust': '厌恶',
            'neutral': '平静',
            'optimism': '乐观',
            'pessimism': '悲观',
            'curiosity': '好奇',
            'boredom': '无聊'
        };
        return emotionMap[emotion] || emotion;
    }

    translatePersonalityTrait(trait) {
        const traitMap = {
            'openness': '开放性',
            'conscientiousness': '责任感',
            'extraversion': '外向性',
            'agreeableness': '亲和性',
            'neuroticism': '神经质',
            'analytical': '分析型',
            'creative': '创造性',
            'practical': '实用性',
            'emotional': '情感型',
            'logical': '逻辑性',
            'intuitive': '直觉型',
            'systematic': '系统性',
            'flexible': '灵活性',
            'detail_oriented': '细节导向',
            'big_picture': '大局观'
        };
        return traitMap[trait] || trait;
    }
    
    displayQualityMetrics(data) {
        console.log('显示质量指标:', data.quality_metrics);

        const metrics = data.quality_metrics || {};

        document.getElementById('node-count').textContent = metrics.node_count || 0;
        document.getElementById('edge-count').textContent = metrics.edge_count || 0;

        const completeness = Math.round((metrics.completeness_score || 0) * 100);
        document.getElementById('completeness-score').textContent = `${completeness}%`;

        const qualityBadge = document.getElementById('quality-badge');
        const quality = metrics.overall_quality || 'low';
        qualityBadge.textContent = this.getQualityText(quality);
        qualityBadge.className = `badge ${this.getQualityClass(quality)}`;
    }
    
    getQualityText(quality) {
        const qualityMap = {
            'high': '高',
            'medium': '中',
            'low': '低'
        };
        return qualityMap[quality] || '未知';
    }
    
    getQualityClass(quality) {
        const classMap = {
            'high': 'bg-success',
            'medium': 'bg-warning',
            'low': 'bg-danger'
        };
        return classMap[quality] || 'bg-secondary';
    }
    
    displayGraph(graphData) {
        const container = document.getElementById('graph-container');
        
        // 隐藏加载指示器
        const loading = document.getElementById('graph-loading');
        if (loading) {
            loading.style.display = 'none';
        }
        
        // 创建网络图容器
        let networkContainer = document.getElementById('graph-network');
        if (!networkContainer) {
            networkContainer = document.createElement('div');
            networkContainer.id = 'graph-network';
            container.appendChild(networkContainer);
        }
        
        // 准备节点数据
        const nodes = new vis.DataSet();
        const edges = new vis.DataSet();
        
        // 处理节点
        if (graphData.nodes) {
            Object.values(graphData.nodes).forEach(node => {
                nodes.add({
                    id: node.node_id,
                    label: this.truncateText(node.content, 20),
                    title: node.content,  // 悬停提示
                    color: this.getNodeColor(node.node_type),
                    size: Math.max(20, node.weight * 50),
                    font: { size: 12, color: '#333' },
                    nodeType: node.node_type,
                    weight: node.weight,
                    fullContent: node.content,
                    metadata: node.metadata
                });
            });
        }
        
        // 处理边
        if (graphData.edges) {
            Object.values(graphData.edges).forEach(edge => {
                edges.add({
                    from: edge.source_id,
                    to: edge.target_id,
                    label: this.getRelationLabel(edge.relation_type),
                    color: { color: '#666', opacity: 0.6 },
                    width: Math.max(1, edge.weight * 5),
                    arrows: 'to',
                    font: { size: 10, color: '#666' },
                    relationType: edge.relation_type,
                    weight: edge.weight,
                    evidence: edge.evidence
                });
            });
        }
        
        this.nodes = nodes;
        this.edges = edges;
        
        // 网络配置
        const options = {
            nodes: {
                shape: 'dot',
                borderWidth: 2,
                borderColor: '#333',
                borderWidthSelected: 3,
                chosen: true
            },
            edges: {
                arrows: {
                    to: { enabled: true, scaleFactor: 0.5 }
                },
                smooth: {
                    enabled: true,
                    type: 'dynamic'
                }
            },
            physics: {
                stabilization: { iterations: 100 },
                barnesHut: {
                    gravitationalConstant: -2000,
                    centralGravity: 0.3,
                    springLength: 95,
                    springConstant: 0.04,
                    damping: 0.09
                }
            },
            interaction: {
                hover: true,
                selectConnectedEdges: false,
                tooltipDelay: 200
            },
            layout: {
                improvedLayout: true
            }
        };
        
        // 创建网络图
        this.network = new vis.Network(networkContainer, { nodes, edges }, options);
        
        // 绑定事件
        this.bindGraphEvents();
    }
    
    bindGraphEvents() {
        if (!this.network) return;
        
        // 节点点击事件
        this.network.on('click', (params) => {
            if (params.nodes.length > 0) {
                const nodeId = params.nodes[0];
                this.showNodeDetail(nodeId);
            }
        });
        
        // 节点悬停事件
        this.network.on('hoverNode', (params) => {
            const nodeId = params.node;
            // 可以添加悬停效果
        });
    }
    
    showNodeDetail(nodeId) {
        const nodeData = this.nodes.get(nodeId);
        if (!nodeData) return;
        
        // 填充模态框数据
        document.getElementById('node-type').textContent = this.getNodeTypeText(nodeData.nodeType);
        document.getElementById('node-weight').textContent = (nodeData.weight || 0).toFixed(2);
        document.getElementById('node-confidence').textContent = 
            ((nodeData.metadata?.confidence || 0.5) * 100).toFixed(0) + '%';
        document.getElementById('node-content').textContent = nodeData.fullContent;
        
        // 显示相关连接
        this.displayNodeConnections(nodeId);
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('nodeDetailModal'));
        modal.show();
    }
    
    displayNodeConnections(nodeId) {
        const container = document.getElementById('node-connections');
        container.innerHTML = '';
        
        const connectedEdges = this.edges.get({
            filter: (edge) => edge.from === nodeId || edge.to === nodeId
        });
        
        if (connectedEdges.length === 0) {
            container.innerHTML = '<span class="text-muted">暂无连接</span>';
            return;
        }
        
        connectedEdges.forEach(edge => {
            const connectionItem = document.createElement('div');
            connectionItem.className = 'connection-item';
            
            const otherNodeId = edge.from === nodeId ? edge.to : edge.from;
            const otherNode = this.nodes.get(otherNodeId);
            const direction = edge.from === nodeId ? '→' : '←';
            
            connectionItem.innerHTML = `
                <span class="connection-type">${this.getRelationLabel(edge.relationType)}</span>
                <span>${direction} ${otherNode.label}</span>
            `;
            
            container.appendChild(connectionItem);
        });
    }
    
    getNodeColor(nodeType) {
        const colorMap = {
            'EXPERIENCE': '#FF4500',
            'BELIEF': '#0079D3',
            'EMOTION': '#EA0027',
            'TOPIC': '#46D160'
        };
        return colorMap[nodeType] || '#666';
    }
    
    getNodeTypeText(nodeType) {
        const typeMap = {
            'EXPERIENCE': '经历体验',
            'BELIEF': '信念价值观',
            'EMOTION': '情绪感受',
            'TOPIC': '话题主题'
        };
        return typeMap[nodeType] || nodeType;
    }
    
    getRelationLabel(relationType) {
        const labelMap = {
            'CAUSES': '导致',
            'INFLUENCES': '影响',
            'CONTRADICTS': '冲突',
            'SUPPORTS': '支持',
            'LEADS_TO': '促成',
            'TRIGGERS': '触发',
            'SIMILAR_TO': '相似'
        };
        return labelMap[relationType] || relationType;
    }
    
    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }
    
    displayDetailedAnalysis(explanation, data) {
        if (!explanation && !data) return;

        // 整体画像
        const portraitElement = document.getElementById('overall-portrait');
        if (explanation?.overall_portrait) {
            portraitElement.textContent = explanation.overall_portrait;
        } else {
            // 从语义分析数据生成整体画像
            const userCharacteristics = data?.semantic_stats?.user_characteristics || {};
            const personalityInsights = userCharacteristics.personality_insights || '暂无数据';
            portraitElement.textContent = personalityInsights;
        }

        // 关键特征
        const traitsElement = document.getElementById('key-traits');
        traitsElement.innerHTML = '';
        if (explanation?.key_traits && explanation.key_traits.length > 0) {
            explanation.key_traits.forEach(trait => {
                const li = document.createElement('li');
                li.innerHTML = `<i class="fas fa-star me-2"></i>${trait}`;
                traitsElement.appendChild(li);
            });
        } else {
            // 从语义分析数据获取关键特征
            const userCharacteristics = data?.semantic_stats?.user_characteristics || {};
            const behavioralPatterns = userCharacteristics.behavioral_patterns || [];
            const valueSystem = userCharacteristics.value_system || [];

            const traits = [...behavioralPatterns.slice(0, 3), ...valueSystem.slice(0, 2)];
            if (traits.length > 0) {
                traits.forEach(trait => {
                    const li = document.createElement('li');
                    li.innerHTML = `<i class="fas fa-star me-2"></i>${trait}`;
                    traitsElement.appendChild(li);
                });
            } else {
                const li = document.createElement('li');
                li.innerHTML = `<i class="fas fa-star me-2"></i>暂无数据`;
                traitsElement.appendChild(li);
            }
        }

        // 核心洞察
        const insightsElement = document.getElementById('main-insights');
        insightsElement.innerHTML = '';
        if (explanation?.main_insights && explanation.main_insights.length > 0) {
            explanation.main_insights.forEach(insight => {
                const li = document.createElement('li');
                li.innerHTML = `<i class="fas fa-lightbulb me-2"></i>${insight}`;
                insightsElement.appendChild(li);
            });
        } else {
            // 从语义分析数据生成洞察
            const parsedQuery = data?.semantic_stats?.parsed_query || {};
            const userCharacteristics = data?.semantic_stats?.user_characteristics || {};

            const insights = [];

            // 从核心关切生成洞察
            if (parsedQuery.core_concerns && parsedQuery.core_concerns.length > 0) {
                insights.push(`主要关注：${parsedQuery.core_concerns.slice(0, 2).join('、')}`);
            }

            // 从决策点生成洞察
            if (parsedQuery.decision_points && parsedQuery.decision_points.length > 0) {
                insights.push(`面临决策：${parsedQuery.decision_points.slice(0, 2).join('、')}`);
            }

            // 从沟通风格生成洞察
            if (userCharacteristics.communication_style) {
                insights.push(`沟通特点：${userCharacteristics.communication_style}`);
            }

            if (insights.length > 0) {
                insights.forEach(insight => {
                    const li = document.createElement('li');
                    li.innerHTML = `<i class="fas fa-lightbulb me-2"></i>${insight}`;
                    insightsElement.appendChild(li);
                });
            } else {
                const li = document.createElement('li');
                li.innerHTML = `<i class="fas fa-lightbulb me-2"></i>暂无数据`;
                insightsElement.appendChild(li);
            }
        }

        // 互动建议
        const recommendationsElement = document.getElementById('interaction-recommendations');
        recommendationsElement.innerHTML = '';
        if (explanation?.interaction_recommendations && explanation.interaction_recommendations.length > 0) {
            explanation.interaction_recommendations.forEach(rec => {
                const li = document.createElement('li');
                li.innerHTML = `<i class="fas fa-comment me-2"></i>${rec}`;
                recommendationsElement.appendChild(li);
            });
        } else {
            // 从语义分析数据生成建议
            const parsedQuery = data?.semantic_stats?.parsed_query || {};
            const userCharacteristics = data?.semantic_stats?.user_characteristics || {};

            const recommendations = [];

            // 基于支持需求生成建议
            if (parsedQuery.support_needs && parsedQuery.support_needs.length > 0) {
                recommendations.push(`提供${parsedQuery.support_needs[0]}方面的支持`);
            }

            // 基于生活领域生成建议
            if (parsedQuery.life_domains && parsedQuery.life_domains.length > 0) {
                recommendations.push(`可以从${parsedQuery.life_domains[0]}角度进行交流`);
            }

            // 基于兴趣生成建议
            const interests = userCharacteristics.interests_and_passions || [];
            if (interests.length > 0) {
                recommendations.push(`可以聊聊${interests[0]}相关话题`);
            }

            if (recommendations.length > 0) {
                recommendations.forEach(rec => {
                    const li = document.createElement('li');
                    li.innerHTML = `<i class="fas fa-comment me-2"></i>${rec}`;
                    recommendationsElement.appendChild(li);
                });
            } else {
                const li = document.createElement('li');
                li.innerHTML = `<i class="fas fa-comment me-2"></i>暂无数据`;
                recommendationsElement.appendChild(li);
            }
        }
    }
    
    // 图谱控制方法
    zoomIn() {
        if (this.network) {
            const scale = this.network.getScale();
            this.network.moveTo({ scale: scale * 1.2 });
        }
    }
    
    zoomOut() {
        if (this.network) {
            const scale = this.network.getScale();
            this.network.moveTo({ scale: scale * 0.8 });
        }
    }
    
    resetView() {
        if (this.network) {
            this.network.fit();
        }
    }
    
    // UI状态管理
    showStatus(message, type = 'info') {
        const statusRow = document.getElementById('status-row');
        const statusAlert = document.getElementById('status-alert');
        const statusText = document.getElementById('status-text');
        
        statusAlert.className = `alert alert-${type}`;
        statusText.textContent = message;
        statusRow.style.display = 'block';
        statusRow.scrollIntoView({ behavior: 'smooth' });
    }
    
    updateStatus(message) {
        const statusText = document.getElementById('status-text');
        if (statusText) {
            statusText.textContent = message;
        }
    }
    
    hideStatus() {
        const statusRow = document.getElementById('status-row');
        statusRow.style.display = 'none';
    }
    
    showResults() {
        const resultsRow = document.getElementById('results-row');
        resultsRow.style.display = 'block';
        resultsRow.scrollIntoView({ behavior: 'smooth' });
    }
    
    hideResults() {
        const resultsRow = document.getElementById('results-row');
        resultsRow.style.display = 'none';
    }
    
    showError(message) {
        this.hideStatus();
        
        const errorModal = document.getElementById('errorModal');
        const errorMessage = document.getElementById('error-message');
        
        errorMessage.textContent = message;
        
        const modal = new bootstrap.Modal(errorModal);
        modal.show();
    }
    
    // 工具方法
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // 评论分析功能
    async analyzeComment() {
        const commentText = document.getElementById('comment-input').value.trim();
        
        if (!commentText) {
            this.showError('请输入要分析的评论内容');
            return;
        }
        
        if (!this.currentAnalysisData) {
            this.showError('请先进行用户画像分析');
            return;
        }
        
        this.showCommentAnalysisLoading();
        
        try {
            const response = await axios.post('/api/analyze_comment', {
                comment_text: commentText,
                user_profile: this.currentAnalysisData
            }, {
                timeout: 60000  // 1分钟超时
            });
            
            if (response.data.success) {
                this.displayCommentAnalysisResult(response.data);
                this.hideCommentAnalysisLoading();
            } else {
                this.showError(response.data.error || '评论分析失败');
                this.hideCommentAnalysisLoading();
            }
        } catch (error) {
            console.error('Comment analysis error:', error);
            this.showError('网络错误或服务暂时不可用');
            this.hideCommentAnalysisLoading();
        }
    }
    
    showCommentAnalysisLoading() {
        document.getElementById('comment-analysis-loading').style.display = 'block';
        document.getElementById('comment-analysis-result').style.display = 'none';
        document.getElementById('analyze-comment-btn').disabled = true;
    }
    
    hideCommentAnalysisLoading() {
        document.getElementById('comment-analysis-loading').style.display = 'none';
        document.getElementById('analyze-comment-btn').disabled = false;
    }
    
    displayCommentAnalysisResult(data) {
        const analysis = data.analysis;
        
        // 显示分析摘要和置信度
        document.getElementById('reasoning-summary').textContent = analysis.reasoning_summary || '暂无分析摘要';
        document.getElementById('confidence-badge').textContent = `置信度: ${Math.round((analysis.confidence_score || 0) * 100)}%`;
        document.getElementById('motivation-badge').textContent = `主要动机: ${analysis.primary_motivation || '未知'}`;
        
        // 显示情绪驱动
        const emotionalDrivers = analysis.emotional_drivers || {};
        document.getElementById('dominant-emotion').textContent = emotionalDrivers.dominant_emotion || '-';
        document.getElementById('emotional-intensity').textContent = this.translateIntensity(emotionalDrivers.emotional_intensity) || '-';
        document.getElementById('emotional-needs').textContent = (emotionalDrivers.emotional_needs || []).join(', ') || '-';
        
        // 显示心理机制
        const psychReasoning = analysis.psychological_reasoning || {};
        document.getElementById('underlying-needs').textContent = (psychReasoning.underlying_needs || []).join(', ') || '-';
        document.getElementById('cognitive-patterns').textContent = (psychReasoning.cognitive_patterns || []).join(', ') || '-';
        
        // 显示行为解释
        const behavioralExplanation = analysis.behavioral_explanation || {};
        document.getElementById('posting-trigger').textContent = behavioralExplanation.posting_trigger || '-';
        document.getElementById('social-function').textContent = behavioralExplanation.social_function || '-';
        
        // 显示背景因素
        const contextualFactors = analysis.contextual_factors || {};
        document.getElementById('life-context').textContent = contextualFactors.life_context || '-';
        document.getElementById('social-context').textContent = contextualFactors.social_context || '-';
        
        // 显示结果
        document.getElementById('comment-analysis-result').style.display = 'block';
        
        // 自动展开评论分析手风琴（如果当前未展开）
        const collapseElement = document.getElementById('comment-analysis-collapse');
        if (!collapseElement.classList.contains('show')) {
            const button = document.querySelector('[data-bs-target="#comment-analysis-collapse"]');
            if (button) {
                button.click();
            }
        }
    }
    
    translateIntensity(intensity) {
        const translations = {
            'high': '高',
            'medium': '中',
            'low': '低'
        };
        return translations[intensity] || intensity;
    }
    
    displayTargetContentAnalysis(data) {
        // 检查是否有目标内容分析结果
        const targetAnalysis = data.target_content_analysis;
        
        if (targetAnalysis && targetAnalysis.success) {
            console.log('发现目标内容分析，自动展示');
            
            // 获取目标内容
            const contentText = targetAnalysis.content_text;
            const contentType = targetAnalysis.content_type;
            
            // 预填充评论输入框
            const commentInput = document.getElementById('comment-input');
            if (commentInput) {
                commentInput.value = contentText;
                commentInput.readOnly = true; // 设为只读，表示这是目标内容
                
                // 添加特殊样式标识这是目标内容
                commentInput.classList.add('target-content');
            }
            
            // 自动显示分析结果
            this.displayCommentAnalysisResult({
                success: true,
                analysis: targetAnalysis.analysis,
                comment_text: contentText
            });
            
            // 修改标题和提示，表明这是目标内容分析
            const analysisHeading = document.querySelector('#comment-analysis-heading .accordion-button');
            if (analysisHeading) {
                const icon = analysisHeading.querySelector('i');
                const originalText = analysisHeading.childNodes[analysisHeading.childNodes.length - 1].textContent;
                analysisHeading.innerHTML = '';
                analysisHeading.appendChild(icon);
                analysisHeading.appendChild(document.createTextNode(`基于画像分析：为什么发出这条${contentType === 'post' ? '帖子' : '评论'}？`));
            }
            
            // 在分析结果前添加目标内容展示
            this.displayTargetContentInfo(data);
            
            // 自动展开评论分析手风琴
            const collapseElement = document.getElementById('comment-analysis-collapse');
            if (!collapseElement.classList.contains('show')) {
                const button = document.querySelector('[data-bs-target="#comment-analysis-collapse"]');
                if (button) {
                    button.click();
                }
            }
            
            // 隐藏分析按钮，因为已经自动分析了
            const analyzeBtn = document.getElementById('analyze-comment-btn');
            if (analyzeBtn) {
                analyzeBtn.style.display = 'none';
            }
            
            // 添加提示说明
            const helpText = document.querySelector('#comment-analysis-collapse .text-muted');
            if (helpText) {
                helpText.textContent = '系统已自动分析该用户发出此内容的心理动机';
            }
            
        } else {
            // 没有目标内容分析，恢复正常状态
            const commentInput = document.getElementById('comment-input');
            if (commentInput) {
                commentInput.readOnly = false;
                commentInput.classList.remove('target-content');
                commentInput.placeholder = '请输入想要分析理由的评论内容...';
            }
            
            const analyzeBtn = document.getElementById('analyze-comment-btn');
            if (analyzeBtn) {
                analyzeBtn.style.display = 'inline-block';
            }
        }
    }
    
    displayTargetContentInfo(data) {
        // 在评论分析结果前添加目标内容信息展示
        const analysisResult = document.getElementById('comment-analysis-result');
        
        // 移除之前的目标内容信息（如果有）
        const existingInfo = analysisResult.querySelector('.target-content-info');
        if (existingInfo) {
            existingInfo.remove();
        }
        
        // 创建目标内容信息展示
        const targetInfo = document.createElement('div');
        targetInfo.className = 'target-content-info alert alert-primary mb-3';
        
        const targetAnalysis = data.target_content_analysis;
        const contentType = targetAnalysis.content_type;
        
        let contentHtml = `
            <h6 class="alert-heading">
                <i class="fas fa-${contentType === 'post' ? 'file-alt' : 'comment'} me-2"></i>
                目标${contentType === 'post' ? '帖子' : '评论'}内容
            </h6>
            <div class="target-content-text bg-light p-3 rounded mb-2">
                "${targetAnalysis.content_text}"
            </div>
        `;
        
        // 添加原始信息
        if (data.original_comment) {
            contentHtml += `
                <div class="small text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    来源：r/${data.original_comment.subreddit} · 
                    评分：${data.original_comment.score} · 
                    时间：${new Date(data.original_comment.timestamp).toLocaleDateString()}
                </div>
            `;
        } else if (data.original_post) {
            contentHtml += `
                <div class="small text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    来源：r/${data.original_post.subreddit} · 
                    评分：${data.original_post.score} · 
                    时间：${new Date(data.original_post.timestamp).toLocaleDateString()}
                </div>
            `;
        }
        
        targetInfo.innerHTML = contentHtml;
        
        // 在分析结果前插入
        analysisResult.insertBefore(targetInfo, analysisResult.firstChild);
    }
    
    /**
     * 从后端拉取最近一次分析结果并展示
     */
    async loadLastResult() {
        try {
            console.log('正在加载历史分析结果...');
            const response = await axios.get('/api/last_result', { timeout: 10000 });
            console.log('API响应:', response.data);

            if (response.data && response.data.success) {
                console.log('找到历史数据，开始显示结果');
                this.displayResults(response.data);
                this.hideStatus();
                this.showResults();
                console.log('历史结果显示完成');
            } else {
                console.log('没有有效的历史数据');
            }
        } catch (err) {
            console.log('无历史分析结果或获取失败', err);
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.analyzer = new RedditProfileAnalyzer();
});

// 开发模式：添加测试按钮
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    document.addEventListener('DOMContentLoaded', () => {
        // 添加测试按钮
        const testBtn = document.createElement('button');
        testBtn.className = 'btn btn-outline-secondary btn-sm';
        testBtn.innerHTML = '<i class="fas fa-flask me-1"></i>测试数据';
        testBtn.style.position = 'fixed';
        testBtn.style.bottom = '20px';
        testBtn.style.right = '20px';
        testBtn.style.zIndex = '9999';

        testBtn.addEventListener('click', () => {
            if (window.analyzer) {
                const mockData = window.analyzer.generateMockData();
                window.analyzer.displayResults(mockData);
                window.analyzer.hideStatus();
                window.analyzer.showResults();
            }
        });

        // 添加加载历史数据按钮
        const loadBtn = document.createElement('button');
        loadBtn.className = 'btn btn-outline-primary btn-sm';
        loadBtn.innerHTML = '<i class="fas fa-history me-1"></i>加载历史';
        loadBtn.style.position = 'fixed';
        loadBtn.style.bottom = '60px';
        loadBtn.style.right = '20px';
        loadBtn.style.zIndex = '9999';

        loadBtn.addEventListener('click', () => {
            if (window.analyzer) {
                console.log('手动触发加载历史数据');
                window.analyzer.loadLastResult();
            }
        });

        document.body.appendChild(testBtn);
        document.body.appendChild(loadBtn);
    });
}