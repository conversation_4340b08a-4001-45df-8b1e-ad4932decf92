#!/usr/bin/env python3
import sys
import os

# 确保只导入 only_profile 目录
sys.path.insert(0, 'only_profile')

try:
    # 直接从 graph_builder 模块导入
    import graph_builder
    NodeType = graph_builder.NodeType

    print("NodeType attributes:")
    for attr in dir(NodeType):
        if not attr.startswith('_') and not callable(getattr(NodeType, attr)):
            print(f"  {attr} = {getattr(NodeType, attr)}")

    print("\nTesting SKILL attribute:")
    print(f"NodeType.SKILL = {NodeType.SKILL}")

    # 测试构建器
    builder = graph_builder.PersonalityGraphBuilder()
    result = builder._parse_node_type("PERSONALITY")
    print(f"Parse PERSONALITY -> {result}")

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
