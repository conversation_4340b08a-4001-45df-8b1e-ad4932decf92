"""
Replicate AI服务模块
替换DeepInfra服务，使用Replicate API进行AI推理
"""
import asyncio
import json
import logging
import os
import re
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

import replicate
from dotenv import load_dotenv
from llm_prompt_manager import get_prompt_config, PromptType

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)

class ReplicateAIService:
    """Replicate AI服务类，替换原有的DeepInfra服务"""

    # 全局统计（跨实例共享）
    _global_call_counter: int = 0
    _global_tokens_used: int = 0

    def __init__(self):
        """初始化Replicate AI服务"""
        self.api_token = os.getenv("REPLICATE_API_TOKEN")
        if not self.api_token:
            raise ValueError("缺少REPLICATE_API_TOKEN环境变量")

        # 设置API token
        replicate.api_token = self.api_token

        # 模型配置
        self.default_model = "openai/gpt-4o-mini"
        self.embedding_model = "sentence-transformers/all-mpnet-base-v2"  # 用于embedding的模型

        # 请求配置
        self.default_temperature = 0.5
        self.default_max_tokens = 2000
        self.default_timeout = 300  # 5分钟超时

        logger.info(f"Replicate AI服务初始化完成，使用模型: {self.default_model}")

        # 实例级统计
        self.instance_call_counter = 0

    @staticmethod
    def _estimate_tokens(text: str) -> int:
        """粗略估算token数量（平均4字符≈1 token）"""
        return max(1, int(len(text) / 4))

    async def get_completion(self,
                           prompt: str,
                           system_prompt: str,
                           temperature: float,
                           max_tokens: int,
                           model: Optional[str] = None) -> str:
        """
        获取AI文本完成

        Args:
            prompt: 用户提示
            system_prompt: 系统提示
            temperature: 温度参数
            max_tokens: 最大token数
            model: 使用的模型

        Returns:
            AI响应文本
        """
        try:
            # 使用默认模型（如果未提供）
            model = model or self.default_model

            # 构建输入
            input_data = {
                "prompt": prompt,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "system_prompt": system_prompt
            }

            # 统计调用
            ReplicateAIService._global_call_counter += 1
            self.instance_call_counter += 1

            tokens_prompt_est = self._estimate_tokens(prompt)

            logger.debug(
                "发送Replicate请求 | 全局调用#%d 实例调用#%d | 估算PromptTokens=%d | model=%s",
                ReplicateAIService._global_call_counter,
                self.instance_call_counter,
                tokens_prompt_est,
                model,
            )

            # 调用Replicate API
            output = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: replicate.run(model, input=input_data)
            )

            # 处理输出
            if isinstance(output, list):
                response = "".join(str(item) for item in output)
            elif hasattr(output, '__iter__') and not isinstance(output, str):
                response = "".join(str(item) for item in output)
            else:
                response = str(output)

            # 估算响应token
            tokens_response_est = self._estimate_tokens(response)
            total_tokens = tokens_prompt_est + tokens_response_est
            ReplicateAIService._global_tokens_used += total_tokens

            logger.info(
                "LLM调用完成 | 全局调用#%d | PromptTokens=%d, ResponseTokens=%d, TotalTokens=%d, GlobalTokens=%d",
                ReplicateAIService._global_call_counter,
                tokens_prompt_est,
                tokens_response_est,
                total_tokens,
                ReplicateAIService._global_tokens_used,
            )

            logger.debug(f"Replicate响应长度: {len(response)}")
            return response

        except Exception as e:
            logger.error(f"Replicate API调用失败: {e}")
            raise Exception(f"AI服务调用失败: {str(e)}")

    async def get_embedding(self, text: str) -> List[float]:
        """
        获取文本embedding
        注意：当前Replicate上的embedding模型可能不可用，返回模拟向量

        Args:
            text: 输入文本

        Returns:
            embedding向量
        """
        try:
            logger.debug(f"获取embedding，文本长度: {len(text)}")

            # 暂时返回基于文本哈希的模拟向量，避免API调用失败
            import hashlib
            import numpy as np

            # 使用文本内容生成确定性的向量
            text_hash = hashlib.md5(text.encode()).hexdigest()
            np.random.seed(int(text_hash[:8], 16))  # 使用哈希前8位作为种子
            embedding = np.random.normal(0, 1, 768).tolist()  # 生成768维向量

            logger.debug(f"生成模拟embedding，维度: {len(embedding)}")
            return embedding

        except Exception as e:
            logger.error(f"获取embedding失败: {e}")
            # 返回默认向量而不是抛出异常
            return [0.0] * 768

    def _extract_json_from_response(self, response: str) -> str:
        """
        从AI响应中提取JSON内容
        支持多种格式的JSON提取

        Args:
            response: AI响应文本

        Returns:
            提取的JSON字符串
        """
        try:
            # 策略1：直接尝试解析整个响应
            if response.strip().startswith('{') and response.strip().endswith('}'):
                if self._validate_json(response.strip()):
                    return response.strip()

            # 策略2：查找JSON代码块
            json_patterns = [
                r'```json\s*(.*?)\s*```',
                r'```\s*(.*?)\s*```',
                r'(\{(?:[^{}]|{[^}]*})*\})'  # 匹配嵌套JSON对象
            ]

            for pattern in json_patterns:
                matches = re.findall(pattern, response, re.DOTALL)
                for match in matches:
                    match = match.strip()
                    if match.startswith('{') and match.endswith('}') and self._validate_json(match):
                        logger.debug("通过正则表达式成功提取JSON")
                        return match

            # 策略3：查找第一个完整的JSON对象
            json_candidate = self._find_complete_json_object(response)
            if json_candidate and self._validate_json(json_candidate):
                logger.debug("找到完整JSON对象")
                return json_candidate

            # 策略4：智能修复常见JSON错误
            repaired_json = self._attempt_json_repair(response)
            if repaired_json:
                logger.debug("通过智能修复成功提取JSON")
                return repaired_json

            # 如果所有策略都失败，抛出异常
            logger.error(f"无法从响应中提取有效JSON，响应长度: {len(response)}")
            raise ValueError("无法提取有效的JSON内容")

        except Exception as e:
            logger.error(f"JSON提取失败: {e}")
            raise

    def _validate_json(self, json_str: str) -> bool:
        """验证JSON字符串是否有效"""
        try:
            json.loads(json_str)
            return True
        except (json.JSONDecodeError, TypeError):
            return False

    def _find_complete_json_object(self, text: str) -> Optional[str]:
        """在文本中查找完整的JSON对象"""
        try:
            start_idx = text.find('{')
            if start_idx == -1:
                return None

            brace_count = 0
            for i, char in enumerate(text[start_idx:], start_idx):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        return text[start_idx:i+1]

            return None
        except Exception:
            return None

    def _attempt_json_repair(self, text: str) -> Optional[str]:
        """尝试修复常见的JSON错误"""
        try:
            # 移除多余的文本，只保留JSON部分
            text = text.strip()

            # 查找可能的JSON开始和结束
            start_idx = text.find('{')
            if start_idx == -1:
                return None

            # 尝试找到最后一个}
            end_idx = text.rfind('}')
            if end_idx == -1 or end_idx <= start_idx:
                return None

            json_candidate = text[start_idx:end_idx+1]

            # 尝试一些常见的修复
            repairs = [
                json_candidate,
                json_candidate.replace("'", '"'),  # 单引号转双引号
                json_candidate.replace('True', 'true').replace('False', 'false').replace('None', 'null'),  # Python布尔值转JSON
                re.sub(r',\s*}', '}', json_candidate),  # 移除尾随逗号
                re.sub(r',\s*]', ']', json_candidate),  # 移除数组尾随逗号
            ]

            for repair in repairs:
                if self._validate_json(repair):
                    return repair

            return None
        except Exception:
            return None

    async def get_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """
        批量获取embedding

        Args:
            texts: 文本列表

        Returns:
            embedding向量列表
        """
        try:
            embeddings = []
            for text in texts:
                embedding = await self.get_embedding(text)
                embeddings.append(embedding)
            return embeddings
        except Exception as e:
            logger.error(f"批量获取embedding失败: {e}")
            # 返回默认向量列表
            return [[0.0] * 768 for _ in texts]

    async def close(self):
        """关闭服务（清理资源）"""
        try:
            logger.info("Replicate AI服务关闭")
        except Exception as e:
            logger.error(f"关闭Replicate AI服务时发生错误: {e}")

# 兼容性函数，保持与原有AIService接口一致
class AIService(ReplicateAIService):
    """AIService别名，保持向后兼容"""
    pass

# 测试函数
async def test_replicate_service():
    """测试Replicate服务"""
    try:
        service = ReplicateAIService()

        # 测试文本完成（使用Prompt Manager）
        print("测试文本完成...")
        
        prompt_config = get_prompt_config(
            PromptType.CONTENT_SUMMARIZATION,
            content_length=len("人工智能（AI）是计算机科学的一个分支..."),
            content_type="introduction",
            content="人工智能（AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。"
        )
        
        # 只传递 get_completion 需要的参数
        completion_args = {
            "prompt": prompt_config["prompt"],
            "system_prompt": prompt_config["system_prompt"],
            "temperature": prompt_config["temperature"],
            "max_tokens": prompt_config["max_tokens"],
            "model": prompt_config["model"]
        }

        response = await service.get_completion(**completion_args)
        print(f"响应: {response[:100]}...")

        # 测试JSON提取
        print("\n测试JSON提取...")
        
        json_prompt_config = get_prompt_config(
            PromptType.EMOTION_ANALYSIS, # 使用一个返回JSON的模板
            content="我今天非常高兴，因为我完成了一个困难的项目！"
        )
        
        # 只传递 get_completion 需要的参数
        json_completion_args = {
            "prompt": json_prompt_config["prompt"],
            "system_prompt": json_prompt_config["system_prompt"],
            "temperature": json_prompt_config["temperature"],
            "max_tokens": json_prompt_config["max_tokens"],
            "model": json_prompt_config["model"]
        }
        
        json_response = await service.get_completion(**json_completion_args)
        print(f"JSON响应: {json_response}")

        try:
            extracted_json = service._extract_json_from_response(json_response)
            parsed_data = json.loads(extracted_json)
            print(f"提取的JSON: {parsed_data}")
        except Exception as e:
            print(f"JSON提取失败: {e}")

        # 测试embedding
        print("\n测试embedding...")
        embedding = await service.get_embedding("这是一个测试文本")
        print(f"Embedding维度: {len(embedding)}")

        await service.close()
        print("\n测试完成！")

    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_replicate_service())
