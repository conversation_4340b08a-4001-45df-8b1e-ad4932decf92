#!/usr/bin/env python3
"""
测试评论理由分析功能
验证新添加的评论理由分析API和前端展示功能
"""

import asyncio
import json
import sys
import os

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'only_profile'))

from semantic_analyzer import ProfileSemanticAnalyzer

async def test_comment_reasoning_analysis():
    """测试评论理由分析功能"""
    print("=" * 60)
    print("测试评论理由分析功能")
    print("=" * 60)
    
    # 创建语义分析器
    analyzer = ProfileSemanticAnalyzer()
    
    try:
        # 模拟用户画像数据
        mock_user_profile = {
            'user_characteristics': {
                'personality_traits': {
                    'openness': 0.7,
                    'conscientiousness': 0.6,
                    'extraversion': 0.4,
                    'agreeableness': 0.8,
                    'neuroticism': 0.3
                },
                'behavioral_patterns': {
                    'posting_motivation': 'help_seeking',
                    'engagement_style': 'active',
                    'conflict_handling': 'diplomatic'
                },
                'communication_style': {
                    'formality': 'casual',
                    'tone': 'positive',
                    'verbosity': 'detailed',
                    'emotional_expression': 'medium'
                },
                'interests_and_values': {
                    'main_interests': ['科技', '职业发展', '心理健康'],
                    'core_values': ['诚实', '进步', '帮助他人'],
                    'life_priorities': ['工作成长', '人际关系', '自我提升']
                },
                'psychological_indicators': {
                    'stress_level': 'medium',
                    'life_satisfaction': 'medium',
                    'social_connectivity': 'somewhat_connected',
                    'future_orientation': 'optimistic'
                }
            },
            'parsed_query': {
                'core_concerns': ['职业发展', '技能提升'],
                'emotional_state': {
                    'anxiety': 0.3,
                    'hope': 0.6,
                    'curiosity': 0.7
                }
            }
        }
        
        # 测试评论列表
        test_comments = [
            "谢谢大家的建议，我觉得我应该更加主动地寻求导师的指导。",
            "这个方法我试过，但是感觉效果不是很明显，可能是我理解错了。",
            "我也有类似的经历，当时我选择了直接和老板沟通，结果比想象的要好。",
            "说得很对，我们应该关注的是解决问题，而不是抱怨。",
            "不太同意这个观点，我觉得每个人的情况都不一样，不能一概而论。"
        ]
        
        print(f"开始测试 {len(test_comments)} 条评论的理由分析...")
        print()
        
        for i, comment in enumerate(test_comments, 1):
            print(f"测试 {i}/{len(test_comments)}: {comment[:30]}...")
            
            try:
                result = await analyzer.analyze_comment_reasoning(comment, mock_user_profile)
                
                if result['success']:
                    analysis = result['analysis']
                    print(f"✅ 分析成功")
                    print(f"   主要动机: {analysis.get('primary_motivation', '未知')}")
                    print(f"   置信度: {analysis.get('confidence_score', 0):.2f}")
                    print(f"   分析摘要: {analysis.get('reasoning_summary', '无')[:100]}...")
                    
                    # 显示情绪驱动
                    emotional_drivers = analysis.get('emotional_drivers', {})
                    if emotional_drivers:
                        print(f"   主导情绪: {emotional_drivers.get('dominant_emotion', '未知')}")
                    
                    # 显示行为解释
                    behavioral = analysis.get('behavioral_explanation', {})
                    if behavioral:
                        print(f"   社交功能: {behavioral.get('social_function', '未知')}")
                    
                else:
                    print(f"❌ 分析失败: {result.get('error')}")
                    
            except Exception as e:
                print(f"❌ 测试出错: {e}")
            
            print("-" * 40)
            
        print("\n测试完成!")
        
    except Exception as e:
        print(f"❌ 总体测试失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await analyzer.close()

async def test_api_integration():
    """测试API集成（需要服务器运行）"""
    print("\n" + "=" * 60)
    print("测试API集成功能（需要服务器运行）")
    print("=" * 60)
    
    try:
        import requests
        
        # 测试健康检查
        print("1. 测试健康检查...")
        response = requests.get('http://127.0.0.1:5000/api/health', timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            
            # 测试评论分析API
            print("2. 测试评论分析API...")
            test_data = {
                'comment_text': '谢谢大家的建议，我觉得应该更主动一些。',
                'user_profile': None  # 使用最近的分析结果
            }
            
            response = requests.post(
                'http://127.0.0.1:5000/api/analyze_comment', 
                json=test_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ 评论分析API工作正常")
                    print(f"   分析结果: {result.get('analysis', {}).get('reasoning_summary', '')[:100]}...")
                else:
                    print(f"⚠️ API返回错误: {result.get('error')}")
            else:
                print(f"❌ API请求失败: {response.status_code}")
                
        else:
            print(f"❌ 服务器健康检查失败: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("⚠️ 无法连接到服务器，请确保服务器正在运行")
        print("   运行命令: cd only_profile && python app.py")
    except Exception as e:
        print(f"❌ API测试失败: {e}")

def print_usage_instructions():
    """打印使用说明"""
    print("\n" + "=" * 60)
    print("使用说明")
    print("=" * 60)
    print("1. 启动服务器:")
    print("   cd only_profile")
    print("   python app.py")
    print()
    print("2. 打开浏览器访问:")
    print("   http://127.0.0.1:5000")
    print()
    print("3. 自动目标内容分析（新功能）:")
    print("   - 输入Reddit评论或帖子链接")
    print("   - 系统会自动分析用户画像")
    print("   - 同时自动分析该评论/帖子的发布理由")
    print("   - 在'评论理由分析'模块查看结果")
    print("   - 支持评论链接和帖子链接")
    print()
    print("4. 手动评论分析:")
    print("   - 对于用户链接，先进行用户画像分析")
    print("   - 在分析结果页面找到'评论理由分析'模块")
    print("   - 输入要分析的评论内容")
    print("   - 点击'分析评论理由'按钮")
    print("   - 查看基于用户画像的评论动机分析")
    print()
    print("5. 功能特点:")
    print("   - 基于用户画像分析评论动机")
    print("   - 提供心理学层面的解释")
    print("   - 包含情绪驱动、行为解释等维度")
    print("   - 支持Ctrl+Enter快捷键")
    print("   - 自动识别并分析目标内容")
    print()
    print("6. 支持的链接类型:")
    print("   - 评论链接: reddit.com/r/subreddit/comments/postid/title/commentid/")
    print("   - 帖子链接: reddit.com/r/subreddit/comments/postid/title/")
    print("   - 用户链接: reddit.com/u/username （手动分析）")

async def main():
    """主测试函数"""
    # 测试语义分析功能
    await test_comment_reasoning_analysis()
    
    # 测试API集成
    await test_api_integration()
    
    # 打印使用说明
    print_usage_instructions()

if __name__ == "__main__":
    asyncio.run(main()) 