#!/usr/bin/env python3
"""
简单的语义分析器优化测试
验证删除第一步后的效果
"""

import sys
import os
import json
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'only_profile'))

def test_analyzer_structure():
    """测试分析器结构"""
    print("测试语义分析器结构...")
    
    try:
        from semantic_analyzer import ProfileSemanticAnalyzer
        
        analyzer = ProfileSemanticAnalyzer()
        
        # 检查关键配置
        print(f"✅ 最大内容长度: {analyzer.max_content_length}")
        print(f"✅ 内容采样大小: {analyzer.content_sample_size}")
        print(f"✅ 分析策略: {analyzer.analysis_strategy}")
        
        # 检查是否有新的单步分析方法
        has_comprehensive = hasattr(analyzer, '_analyze_comprehensive_profile')
        print(f"✅ 全面分析方法存在: {has_comprehensive}")
        
        # 检查是否还有旧的分步方法（应该被重命名了）
        has_old_parse = hasattr(analyzer, '_parse_user_content_with_ai_deprecated')
        has_old_chars = hasattr(analyzer, '_analyze_user_characteristics_deprecated') 
        print(f"✅ 旧方法已重命名: parse={has_old_parse}, chars={has_old_chars}")
        
        return True
        
    except Exception as e:
        print(f"❌ 结构测试失败: {e}")
        return False

def test_content_preparation():
    """测试内容准备功能"""
    print("\n测试内容准备功能...")
    
    try:
        from semantic_analyzer import ProfileSemanticAnalyzer
        from models import RedditUser, RedditPost, RedditComment
        
        analyzer = ProfileSemanticAnalyzer()
        
        # 创建测试数据
        posts = [
            RedditPost(
                id="p1",
                text="这是一个测试帖子，内容足够长，应该被包含在分析中。我们希望所有有效内容都能被分析。",
                timestamp=datetime.now(),
                subreddit="test",
                score=10
            ),
            RedditPost(
                id="p2", 
                text="另一个测试帖子，也有足够的内容长度。",
                timestamp=datetime.now() - timedelta(hours=1),
                subreddit="test2",
                score=20
            )
        ]
        
        comments = []
        for i in range(30):
            comments.append(
                RedditComment(
                    id=f"c{i}",
                    text=f"这是第{i}个测试评论，内容足够长以确保被包含在分析中。",
                    timestamp=datetime.now() - timedelta(minutes=i*30),
                    subreddit=f"test{i%5}",
                    score=max(1, 20-i)
                )
            )
        
        test_user = RedditUser(
            username="test_user",
            posts=posts,
            comments=comments
        )
        
        # 测试内容准备
        content = analyzer._prepare_user_content(test_user)
        
        # 验证结果
        posts_count = content.count("[帖子")
        comments_count = content.count("[评论")
        
        print(f"✅ 准备的内容包含: {posts_count}个帖子, {comments_count}个评论")
        print(f"✅ 内容总长度: {len(content)}字符")
        
        # 验证配置提升
        print(f"✅ 最大内容长度已提升到: {analyzer.max_content_length}")
        print(f"✅ 采样大小已提升到: {analyzer.content_sample_size}")
        
        # 验证使用更多内容
        total_items = len(posts) + len(comments)
        used_items = posts_count + comments_count
        usage_ratio = used_items / total_items if total_items > 0 else 0
        
        print(f"✅ 内容使用率: {used_items}/{total_items} = {usage_ratio:.2%}")
        
        if usage_ratio > 0.8:  # 使用了80%以上的内容
            print("🎉 内容使用率显著提升！")
        elif usage_ratio > 0.5:
            print("✅ 内容使用率良好")
        else:
            print("⚠️ 内容使用率可能偏低")
            
        return True
        
    except Exception as e:
        print(f"❌ 内容准备测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 80)
    print("语义分析器单步优化验证测试")
    print("=" * 80)
    
    tests_passed = 0
    total_tests = 2
    
    # 测试1: 分析器结构
    if test_analyzer_structure():
        tests_passed += 1
    
    # 测试2: 内容准备
    if test_content_preparation():
        tests_passed += 1
    
    print("\n" + "=" * 80)
    print(f"测试结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！单步优化成功实现！")
        print("\n关键改进:")
        print("• 删除了无意义的第一步语义分析")
        print("• 所有内容现在都在一步中进行全面分析")  
        print("• 大幅提升了内容使用率和分析深度")
        print("• 简化了分析流程，提高了效率")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    print("=" * 80)

if __name__ == "__main__":
    main() 