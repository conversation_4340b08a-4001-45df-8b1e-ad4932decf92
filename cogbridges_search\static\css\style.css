/* CogBridges Search - Silicon Valley风格样式 */

/* CSS变量定义 */
:root {
    /* 主色调 - Silicon Valley科技风 */
    --primary-color: #007AFF;
    --primary-hover: #0056CC;
    --primary-light: #E3F2FD;
    
    /* 辅助色调 */
    --secondary-color: #5856D6;
    --accent-color: #FF9500;
    --success-color: #34C759;
    --warning-color: #FF9F0A;
    --error-color: #FF3B30;
    
    /* 中性色调 */
    --text-primary: #1D1D1F;
    --text-secondary: #86868B;
    --text-tertiary: #C7C7CC;
    --background-primary: #FFFFFF;
    --background-secondary: #F2F2F7;
    --background-tertiary: #FAFAFA;
    --border-color: #E5E5EA;
    --shadow-color: rgba(0, 0, 0, 0.1);
    
    /* Reddit品牌色 */
    --reddit-orange: #FF4500;
    --reddit-blue: #0079D3;
    
    /* 字体 */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', monospace;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    --spacing-3xl: 64px;
    
    /* 圆角 */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
    --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.15);
    
    /* 过渡 */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.25s ease-out;
    --transition-slow: 0.35s ease-out;
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-primary);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    user-select: none;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.3);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--background-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--background-tertiary);
    border-color: var(--text-tertiary);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
    background: var(--primary-light);
}

/* 输入框样式 */
.select-input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-family: var(--font-primary);
    font-size: 14px;
    background: var(--background-primary);
    color: var(--text-primary);
    transition: border-color var(--transition-fast);
}

.select-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

/* 头部样式 */
.header {
    background: var(--background-primary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg) 0;
}

.header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.logo i {
    color: var(--reddit-orange);
    font-size: 32px;
}

.header-subtitle {
    font-size: 16px;
    color: var(--text-secondary);
    font-weight: 400;
}

/* 主要内容区域 */
.main {
    min-height: calc(100vh - 200px);
    padding: var(--spacing-3xl) 0;
}

/* 搜索区域 */
.search-section {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.search-container {
    padding: var(--spacing-2xl) 0;
}

.search-title {
    font-size: 48px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    line-height: 1.2;
}

.search-subtitle {
    font-size: 18px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-3xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.search-form {
    margin-bottom: var(--spacing-xl);
}

.search-input-container {
    margin-bottom: var(--spacing-xl);
}

.search-input-wrapper {
    position: relative;
    max-width: 600px;
    margin: 0 auto var(--spacing-lg);
}

.search-input {
    width: 100%;
    padding: 16px 50px 16px 50px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-xl);
    font-family: var(--font-primary);
    font-size: 16px;
    background: var(--background-primary);
    color: var(--text-primary);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1), var(--shadow-md);
}

.search-icon {
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: 16px;
}

.clear-button {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all var(--transition-fast);
    opacity: 0;
    visibility: hidden;
}

.search-input:not(:placeholder-shown) + .clear-button {
    opacity: 1;
    visibility: visible;
}

.clear-button:hover {
    background: var(--background-secondary);
    color: var(--text-primary);
}

/* 搜索选项 */
.search-options {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-xl);
    flex-wrap: wrap;
}

.option-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.option-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 14px;
    color: var(--text-secondary);
    cursor: pointer;
    user-select: none;
}

.option-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all var(--transition-fast);
}

.option-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.option-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* 搜索按钮 */
.search-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* 搜索建议 */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

.suggestions-list {
    padding: var(--spacing-sm) 0;
}

.suggestion-item {
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    transition: background-color var(--transition-fast);
    border-bottom: 1px solid var(--border-color);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover {
    background: var(--background-secondary);
}

/* 加载区域 */
.loading-section {
    text-align: center;
    padding: var(--spacing-3xl) 0;
}

.loading-container {
    max-width: 600px;
    margin: 0 auto;
}

.loading-spinner {
    margin-bottom: var(--spacing-xl);
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xl);
}

.loading-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    gap: var(--spacing-md);
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
    position: relative;
}

.step i {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: var(--background-secondary);
    color: var(--text-secondary);
    font-size: 16px;
    transition: all var(--transition-normal);
}

.step.active i {
    background: var(--primary-color);
    color: white;
}

.step.completed i {
    background: var(--success-color);
    color: white;
}

.step span {
    font-size: 12px;
    color: var(--text-secondary);
    text-align: center;
}

.step.active span {
    color: var(--primary-color);
    font-weight: 500;
}

.step-status {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--success-color);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.step.completed .step-status {
    opacity: 1;
}

.step.completed .step-status::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
    font-weight: bold;
}

/* 进度条 */
.loading-progress {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: var(--background-secondary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal);
    width: 0%;
}

.progress-text {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    min-width: 40px;
    text-align: right;
}

/* 结果区域 */
.results-section {
    padding: var(--spacing-xl) 0;
}

.results-container {
    max-width: 1000px;
    margin: 0 auto;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-xl);
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.results-title {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.results-meta {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    flex: 1;
}

.query-display {
    font-size: 16px;
    color: var(--text-secondary);
    font-family: var(--font-mono);
    background: var(--background-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    display: inline-block;
}

.results-count,
.processing-time {
    font-size: 14px;
    color: var(--text-tertiary);
}

.results-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

/* 统计概览 */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    font-size: 20px;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* 结果标签页 */
.results-tabs {
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.tab-nav {
    display: flex;
    background: var(--background-secondary);
    border-bottom: 1px solid var(--border-color);
}

.tab-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-family: var(--font-primary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    background: var(--background-tertiary);
    color: var(--text-primary);
}

.tab-button.active {
    background: var(--background-primary);
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    padding: var(--spacing-xl);
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 帖子列表 */
.posts-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.post-card {
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.post-card:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
    gap: var(--spacing-md);
}

.post-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.4;
    margin-bottom: var(--spacing-sm);
}

.post-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: 12px;
    color: var(--text-secondary);
    flex-wrap: wrap;
}

.post-subreddit {
    background: var(--reddit-orange);
    color: white;
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.post-score {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.post-content {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
    max-height: 120px;
    overflow: hidden;
    position: relative;
}

.post-content::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(transparent, var(--background-secondary));
}

.post-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.post-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.post-link:hover {
    text-decoration: underline;
}

/* 评论列表 */
.comments-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.comment-card {
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.comment-author {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.comment-score {
    font-size: 12px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.comment-body {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 用户列表 */
.users-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.user-card {
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.user-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 18px;
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.user-karma {
    font-size: 12px;
    color: var(--text-secondary);
}

.user-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.user-stat {
    text-align: center;
    padding: var(--spacing-sm);
    background: var(--background-primary);
    border-radius: var(--radius-sm);
}

.user-stat-number {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.user-stat-label {
    font-size: 11px;
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* 洞察内容 */
.insights-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.insight-card {
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.insight-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.insight-title i {
    color: var(--primary-color);
}

.insight-content {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 错误区域 */
.error-section {
    text-align: center;
    padding: var(--spacing-3xl) 0;
}

.error-container {
    max-width: 500px;
    margin: 0 auto;
}

.error-icon {
    margin-bottom: var(--spacing-xl);
}

.error-icon i {
    font-size: 64px;
    color: var(--error-color);
}

.error-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.error-message {
    font-size: 16px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

.error-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* 页脚 */
.footer {
    background: var(--background-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-xl) 0;
    margin-top: var(--spacing-3xl);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.footer-text {
    color: var(--text-secondary);
    font-size: 14px;
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.footer-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 14px;
    transition: color var(--transition-fast);
}

.footer-link:hover {
    color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .search-title {
        font-size: 36px;
    }

    .search-subtitle {
        font-size: 16px;
    }

    .search-input {
        padding: 14px 45px 14px 45px;
        font-size: 16px;
    }

    .search-options {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .search-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }

    .loading-steps {
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .step {
        flex-direction: row;
        text-align: left;
    }

    .results-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .results-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .stats-overview {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .tab-nav {
        flex-wrap: wrap;
    }

    .tab-button {
        flex: none;
        min-width: 120px;
    }

    .users-list {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .search-title {
        font-size: 28px;
    }

    .search-subtitle {
        font-size: 14px;
    }

    .stats-overview {
        grid-template-columns: 1fr;
    }

    .tab-nav {
        flex-direction: column;
    }

    .tab-button {
        width: 100%;
        justify-content: flex-start;
        padding: var(--spacing-md);
    }

    .post-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .post-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: var(--background-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-secondary);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--text-tertiary);
    border-radius: var(--radius-sm);
    transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* 选择文本样式 */
::selection {
    background: var(--primary-light);
    color: var(--primary-color);
}

/* 焦点样式 */
*:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}
