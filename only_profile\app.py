"""
CogBridges Reddit用户画像分析 - 主程序入口
整合链接解析、数据抓取、语义分析、图谱构建和前端展示模块
"""
import asyncio
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional
import sys
import os
import sqlite3
import json

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'resona'))

from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_cors import CORS
import asyncio

# 导入自定义模块
from local_config import config
from url_parser import RedditUrlParser, RedditLinkType
from data_crawler import DataCrawler
from semantic_analyzer import ProfileSemanticAnalyzer
from graph_builder import PersonalityGraphBuilder

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('only_profile.log')
    ]
)
logger = logging.getLogger(__name__)

class RedditProfileApp:
    """Reddit用户画像分析应用"""
    
    def __init__(self):
        """初始化应用"""
        # 显式指定模板和静态资源目录，防止在不同工作目录启动时找不到文件
        base_dir = os.path.dirname(os.path.abspath(__file__))
        self.app = Flask(
            __name__,
            template_folder=os.path.join(base_dir, 'templates'),
            static_folder=os.path.join(base_dir, 'static')
        )
        self.app.config['SECRET_KEY'] = 'reddit-profile-analyzer-secret-key'
        
        # 启用CORS
        CORS(self.app)
        
        # 初始化组件
        self.url_parser = RedditUrlParser()
        self.data_crawler = None
        self.semantic_analyzer = None
        self.graph_builder = None
        
        # 注册路由
        self.register_routes()
        
        # 应用统计
        self.stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'start_time': datetime.now()
        }
        
        # 初始化持久化数据库（保存分析结果）
        self._init_database()
        
        logger.info("Reddit用户画像分析应用初始化完成")
    
    def register_routes(self):
        """注册路由"""
        
        @self.app.route('/')
        def index():
            """主页"""
            return render_template('index.html')
        
        @self.app.route('/api/analyze', methods=['POST'])
        def analyze_reddit_url():
            """分析Reddit链接的API接口"""
            return asyncio.run(self._analyze_reddit_url_async())
        
        @self.app.route('/api/stats', methods=['GET'])
        def get_stats():
            """获取应用统计信息"""
            return jsonify({
                'success': True,
                'stats': {
                    **self.stats,
                    'uptime_seconds': (datetime.now() - self.stats['start_time']).total_seconds(),
                    'success_rate': (
                        self.stats['successful_analyses'] / max(self.stats['total_analyses'], 1) * 100
                    )
                }
            })
        
        @self.app.route('/api/health', methods=['GET'])
        def health_check():
            """健康检查"""
            return jsonify({
                'success': True,
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'components': {
                    'url_parser': 'ok',
                    'data_crawler': 'ok' if self.data_crawler else 'not_initialized',
                    'semantic_analyzer': 'ok' if self.semantic_analyzer else 'not_initialized',
                    'graph_builder': 'ok' if self.graph_builder else 'not_initialized'
                }
            })
        
        @self.app.route('/api/last_result', methods=['GET'])
        def get_last_result():
            """获取最近一次分析结果"""
            try:
                with sqlite3.connect('data/graph_storage.db') as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT result_json FROM analysis_results ORDER BY id DESC LIMIT 1")
                    row = cursor.fetchone()
                    if row:
                        return jsonify(json.loads(row[0]))
                    return jsonify({'success': False, 'error': '暂无历史记录'})
            except Exception as e:
                logger.error(f"获取历史结果失败: {e}")
                return jsonify({'success': False, 'error': '服务器内部错误'})
        
        @self.app.route('/api/analyze_comment', methods=['POST'])
        def analyze_comment_reasoning():
            """分析特定评论理由的API接口"""
            return asyncio.run(self._analyze_comment_reasoning_async())
        
        @self.app.route('/static/<path:filename>')
        def static_files(filename):
            """静态文件服务"""
            return send_from_directory('static', filename)
        
        @self.app.errorhandler(404)
        def not_found(error):
            """404错误处理"""
            return jsonify({
                'success': False,
                'error': '页面未找到'
            }), 404
        
        @self.app.errorhandler(500)
        def internal_error(error):
            """500错误处理"""
            logger.error(f"Internal error: {error}")
            return jsonify({
                'success': False,
                'error': '服务器内部错误'
            }), 500
    
    async def _analyze_reddit_url_async(self):
        """异步分析Reddit链接"""
        try:
            # 更新统计
            self.stats['total_analyses'] += 1
            
            # 获取请求数据
            data = request.get_json()
            if not data or 'url' not in data:
                return jsonify({
                    'success': False,
                    'error': '缺少必要参数：url'
                })
            
            url = data['url'].strip()
            logger.info(f"开始分析Reddit链接: {url}")
            
            # 初始化组件（延迟初始化以避免启动时的问题）
            await self._ensure_components_initialized()
            
            # 步骤1：解析链接
            link_info = self.url_parser.parse_url(url)
            if not link_info.is_valid:
                self.stats['failed_analyses'] += 1
                return jsonify({
                    'success': False,
                    'error': f'链接解析失败: {link_info.error_message}',
                    'link_type': 'invalid'
                })
            
            logger.info(f"链接解析成功，类型: {link_info.link_type.value}")
            
            # 步骤2：数据抓取
            logger.info("开始数据抓取...")
            crawl_result = await self.data_crawler.crawl_from_url(url)
            
            if not crawl_result['success']:
                self.stats['failed_analyses'] += 1
                return jsonify({
                    'success': False,
                    'error': f'数据抓取失败: {crawl_result["error"]}',
                    'link_type': link_info.link_type.value,
                    'crawl_result': crawl_result
                })
            
            reddit_user = crawl_result['user_data']
            logger.info(f"数据抓取成功，用户: {reddit_user.username}")
            
            # 步骤3：语义分析
            logger.info("开始语义分析...")
            semantic_result = await self.semantic_analyzer.analyze_user_content(reddit_user)
            
            if not semantic_result['success']:
                self.stats['failed_analyses'] += 1
                return jsonify({
                    'success': False,
                    'error': f'语义分析失败: {semantic_result["error"]}',
                    'username': reddit_user.username,
                    'crawl_stats': crawl_result.get('stats', {})
                })
            
            logger.info(f"语义分析成功，置信度: {semantic_result['stats']['confidence_score']:.3f}")
            
            # 步骤4：图谱构建
            logger.info("开始构建人格图谱...")
            graph_result = await self.graph_builder.build_personality_graph(
                reddit_user, semantic_result
            )
            
            if not graph_result['success']:
                self.stats['failed_analyses'] += 1
                return jsonify({
                    'success': False,
                    'error': f'图谱构建失败: {graph_result["error"]}',
                    'username': reddit_user.username,
                    'semantic_stats': semantic_result.get('stats', {})
                })
            
            logger.info(f"图谱构建成功，节点数: {graph_result['quality_metrics']['node_count']}")
            
            # 步骤5：目标内容分析（如果是评论或帖子链接）
            target_content_analysis = None
            if link_info.link_type.value in ['comment', 'post']:
                logger.info("开始分析目标内容的发布理由...")
                target_content_analysis = await self._analyze_target_content(
                    link_info, crawl_result, semantic_result, graph_result
                )
            
            # 构建最终结果
            final_result = self._build_final_result(
                url, link_info, crawl_result, semantic_result, graph_result, target_content_analysis
            )
            
            # 将结果持久化到数据库
            self._save_analysis_result(final_result)
            
            self.stats['successful_analyses'] += 1
            logger.info(f"分析完成，用户: {reddit_user.username}")
            
            return jsonify(final_result)
            
        except Exception as e:
            self.stats['failed_analyses'] += 1
            logger.error(f"分析过程中发生错误: {e}")
            logger.error(traceback.format_exc())
            
            return jsonify({
                'success': False,
                'error': f'分析过程中发生未知错误: {str(e)}',
                'error_type': type(e).__name__
            })
    
    async def _ensure_components_initialized(self):
        """确保组件已初始化"""
        if self.data_crawler is None:
            self.data_crawler = DataCrawler()
            logger.info("数据抓取器初始化完成")
        
        if self.semantic_analyzer is None:
            self.semantic_analyzer = ProfileSemanticAnalyzer()
            logger.info("语义分析器初始化完成")
        
        if self.graph_builder is None:
            self.graph_builder = PersonalityGraphBuilder()
            logger.info("图谱构建器初始化完成")

    async def _analyze_comment_reasoning_async(self):
        """异步分析评论理由"""
        try:
            # 获取请求数据
            data = request.get_json()
            if not data or 'comment_text' not in data:
                return jsonify({
                    'success': False,
                    'error': '缺少必要参数：comment_text'
                })
            
            comment_text = data['comment_text'].strip()
            user_profile_data = data.get('user_profile')
            
            if not comment_text:
                return jsonify({
                    'success': False,
                    'error': '评论内容不能为空'
                })
            
            # 如果没有提供用户画像，尝试从最近的分析结果中获取
            if not user_profile_data:
                try:
                    with sqlite3.connect('data/graph_storage.db') as conn:
                        cursor = conn.cursor()
                        cursor.execute("SELECT result_json FROM analysis_results ORDER BY id DESC LIMIT 1")
                        row = cursor.fetchone()
                        if row:
                            last_result = json.loads(row[0])
                            user_profile_data = last_result
                        else:
                            return jsonify({
                                'success': False,
                                'error': '没有找到用户画像数据，请先进行用户分析'
                            })
                except Exception as e:
                    logger.error(f"获取用户画像数据失败: {e}")
                    return jsonify({
                        'success': False,
                        'error': '无法获取用户画像数据'
                    })
            
            logger.info(f"开始分析评论理由: {comment_text[:50]}...")
            
            # 初始化组件
            await self._ensure_components_initialized()
            
            # 使用语义分析器分析评论理由
            reasoning_result = await self.semantic_analyzer.analyze_comment_reasoning(
                comment_text, user_profile_data
            )
            
            # 检查分析结果是否成功
            if reasoning_result and reasoning_result.get('success', False):
                logger.info("评论理由分析完成")
                return jsonify(reasoning_result)
            else:
                logger.error(f"评论理由分析失败: {reasoning_result.get('error', '未知错误')}")
                return jsonify(reasoning_result)
                
        except Exception as e:
            logger.error(f"评论理由分析过程中发生错误: {e}")
            return jsonify({
                'success': False,
                'error': f'分析过程中发生错误: {str(e)}',
                'traceback': traceback.format_exc()
            })
    
    async def _analyze_target_content(self, link_info, crawl_result: Dict[str, Any], 
                                    semantic_result: Dict[str, Any], graph_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        分析目标评论或帖子的发布理由
        
        Args:
            link_info: 链接信息
            crawl_result: 抓取结果
            semantic_result: 语义分析结果
            graph_result: 图谱构建结果
            
        Returns:
            Dict: 目标内容分析结果
        """
        try:
            # 构建用户画像数据
            user_profile_data = {
                'user_characteristics': semantic_result.get('user_characteristics', {}),
                'parsed_query': semantic_result.get('parsed_query', {}),
                'graph_explanation': graph_result.get('graph_explanation', {}),
                'username': crawl_result.get('username')
            }
            
            # 获取目标内容
            target_content = None
            content_type = None
            
            if link_info.link_type.value == 'comment':
                # 评论链接
                original_comment = crawl_result.get('original_comment')
                if original_comment and hasattr(original_comment, 'text'):
                    target_content = original_comment.text
                    content_type = 'comment'
                    
            elif link_info.link_type.value == 'post':
                # 帖子链接
                original_post = crawl_result.get('original_post')
                if original_post and hasattr(original_post, 'text'):
                    target_content = original_post.text
                    content_type = 'post'
            
            if not target_content:
                logger.warning("未找到目标内容，跳过分析")
                return None
            
            logger.info(f"分析目标{content_type}: {target_content[:50]}...")
            
            # 使用语义分析器分析目标内容的发布理由
            reasoning_result = await self.semantic_analyzer.analyze_comment_reasoning(
                target_content, user_profile_data
            )
            
            # 检查分析结果是否成功（reasoning_result直接是analysis_result，没有success字段）
            if reasoning_result and not reasoning_result.get('error'):
                logger.info("目标内容分析完成")
                # 将原始数据序列化为可JSON化的字典
                def _serialize_comment(cmt):
                    return {
                        'id': cmt.id,
                        'text': cmt.text,
                        'subreddit': cmt.subreddit,
                        'timestamp': cmt.timestamp.isoformat() if hasattr(cmt.timestamp, 'isoformat') else str(cmt.timestamp),
                        'score': cmt.score
                    } if cmt else None

                def _serialize_post(pst):
                    return {
                        'id': pst.id,
                        'text': pst.text,
                        'subreddit': pst.subreddit,
                        'timestamp': pst.timestamp.isoformat() if hasattr(pst.timestamp, 'isoformat') else str(pst.timestamp),
                        'score': pst.score
                    } if pst else None

                orig_comment_obj = crawl_result.get('original_comment') if content_type == 'comment' else None
                orig_post_obj = crawl_result.get('original_post') if content_type == 'post' else None
                parent_comments_objs = crawl_result.get('parent_comments', []) if content_type == 'comment' else []

                return {
                    'success': True,
                    'content_type': content_type,
                    'content_text': target_content,
                    'analysis': reasoning_result,
                    'original_data': {
                        'comment': _serialize_comment(orig_comment_obj),
                        'post': _serialize_post(orig_post_obj),
                        'parent_comments': [_serialize_comment(c) for c in parent_comments_objs]
                    }
                }
            else:
                error_msg = reasoning_result.get('error') if reasoning_result else '分析结果为空'
                logger.error(f"目标内容分析失败: {error_msg}")
                return {
                    'success': False,
                    'content_type': content_type,
                    'content_text': target_content,
                    'error': error_msg
                }
                
        except Exception as e:
            logger.error(f"分析目标内容时发生错误: {e}")
            return {
                'success': False,
                'error': f'分析目标内容失败: {str(e)}'
            }

    def _build_final_result(self, url: str, link_info, crawl_result: Dict[str, Any], 
                          semantic_result: Dict[str, Any], graph_result: Dict[str, Any], 
                          target_content_analysis: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """构建最终结果"""
        
        # 转换图谱数据为前端可用格式
        user_profile = graph_result['user_profile']
        graph_data = self._convert_graph_for_frontend(user_profile.graph)
        
        # 构建基础结果
        result = {
            'success': True,
            'analysis_timestamp': datetime.now().isoformat(),
            'original_url': url,
            'link_type': link_info.link_type.value,
            'username': crawl_result['username'],
            'user_profile': {
                'user_id': user_profile.user_id,
                'completeness_score': user_profile.completeness_score,
                'graph': graph_data,
                'metadata': getattr(user_profile, 'metadata', {})
            },
            'graph_explanation': graph_result['graph_explanation'],
            'quality_metrics': graph_result['quality_metrics'],
            'content_stats': crawl_result['stats'],
            'semantic_stats': semantic_result['stats'],
            'analysis_pipeline': {
                'link_parsing': 'success',
                'data_crawling': 'success',
                'semantic_analysis': 'success',
                'graph_building': 'success',
                'target_content_analysis': 'success' if target_content_analysis and target_content_analysis.get('success') else 'skipped'
            }
        }
        
        # 添加目标内容分析结果（如果有的话）
        if target_content_analysis:
            result['target_content_analysis'] = target_content_analysis
            
            # 添加原始内容信息到结果中，方便前端展示
            if target_content_analysis.get('original_data'):
                original_data = target_content_analysis['original_data']
                if original_data.get('comment'):
                    result['original_comment'] = original_data['comment']
                if original_data.get('post'):
                    result['original_post'] = original_data['post']
                if original_data.get('parent_comments'):
                    result['parent_comments'] = original_data['parent_comments']
        
        return result
    
    def _convert_graph_for_frontend(self, user_graph) -> Dict[str, Any]:
        """转换图谱数据为前端可用格式"""
        try:
            # 转换节点
            nodes = {}
            for node_id, node in user_graph.nodes.items():
                nodes[node_id] = {
                    'node_id': node.node_id,
                    'node_type': node.node_type.value if hasattr(node.node_type, 'value') else str(node.node_type),
                    'content': node.content,
                    'weight': node.weight,
                    'metadata': getattr(node, 'metadata', {})
                }
            
            # 转换边
            edges_list = []
            for edge in user_graph.edges:
                edges_list.append({
                    'source_id': edge.source_node_id,  # 修复：使用正确的属性名
                    'target_id': edge.target_node_id,  # 修复：使用正确的属性名
                    'relation_type': edge.relation_type.value if hasattr(edge.relation_type, 'value') else str(edge.relation_type),
                    'weight': edge.weight,
                    'evidence': getattr(edge, 'evidence', '')
                })
            
            return {
                'nodes': nodes,
                'edges': edges_list,
                'user_id': user_graph.user_id
            }
            
        except Exception as e:
            logger.error(f"转换图谱数据时发生错误: {e}")
            return {
                'nodes': {},
                'edges': {},
                'user_id': 'unknown'
            }
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.data_crawler:
                await self.data_crawler.close()
            if self.semantic_analyzer:
                await self.semantic_analyzer.close()
            if self.graph_builder:
                await self.graph_builder.close()
            logger.info("应用资源清理完成")
        except Exception as e:
            logger.error(f"清理资源时发生错误: {e}")
    
    def run(self, host='127.0.0.1', port=5000, debug=False):
        """运行应用"""
        logger.info(f"启动Reddit用户画像分析应用 - http://{host}:{port}")
        
        try:
            self.app.run(host=host, port=port, debug=debug)
        except KeyboardInterrupt:
            logger.info("接收到停止信号")
        finally:
            # 异步清理需要在事件循环中执行
            try:
                asyncio.run(self.cleanup())
            except Exception as e:
                logger.error(f"应用清理失败: {e}")

    def _init_database(self):
        """初始化analysis_results表"""
        try:
            os.makedirs('data', exist_ok=True)
            with sqlite3.connect('data/graph_storage.db') as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS analysis_results (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT,
                        created_at TIMESTAMP,
                        result_json TEXT
                    )
                """)
                conn.commit()
            logger.info("分析结果持久化表初始化完成")
        except Exception as e:
            logger.error(f"初始化持久化数据库失败: {e}")

    def _save_analysis_result(self, result: Dict[str, Any]):
        """保存分析结果到SQLite"""
        try:
            # 自定义JSON序列化器处理datetime对象
            def json_serial(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

            with sqlite3.connect('data/graph_storage.db') as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "INSERT INTO analysis_results (username, created_at, result_json) VALUES (?, ?, ?)",
                    (result.get('username'), datetime.now().isoformat(), json.dumps(result, ensure_ascii=False, default=json_serial))
                )
                conn.commit()
            logger.info(f"已持久化分析结果：{result.get('username')}")
        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")

def create_app():
    """创建Flask应用实例"""
    app_instance = RedditProfileApp()
    return app_instance.app

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Reddit用户画像分析应用')
    parser.add_argument('--host', default='127.0.0.1', help='主机地址 (默认: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=5000, help='端口号 (默认: 5000)')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    
    args = parser.parse_args()
    
    # 创建并运行应用
    app = RedditProfileApp()
    app.run(host=args.host, port=args.port, debug=args.debug)

if __name__ == '__main__':
    main() 