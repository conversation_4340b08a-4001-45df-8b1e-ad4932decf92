only_profile 项目测试报告
================================================================================
测试时间: 2025-07-07 15:48:08
Python版本: 3.12.5 (tags/v3.12.5:ff3bc82, Aug  6 2024, 20:45:27) [MSC v.1940 64 bit (AMD64)]
测试环境: D:\pycharmproject\CogBridges_v020\.venv

总体结果:
  总测试数: 4
  成功测试: 2
  失败测试: 2
  成功率: 50.0%
  总耗时: 0.71 秒

详细结果:
------------------------------------------------------------
test_only_profile_url_parser.py: ✗ 失败 (0.45秒)

test_only_profile_data_crawler.py: ✗ 失败 (0.13秒)

test_only_profile_config.py: ✓ 成功 (0.08秒)

test_only_profile_integration.py: ✓ 成功 (0.04秒)

建议:
------------------------------------------------------------
✗ 存在失败的测试，需要检查和修复。
  建议:
  1. 检查失败测试的错误信息
  2. 确认项目依赖已正确安装
  3. 检查项目配置是否正确
  4. 确认测试环境设置正确

================================================================================