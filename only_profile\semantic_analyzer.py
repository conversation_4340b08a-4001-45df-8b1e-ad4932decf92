"""
语义分析模块
复用resona的语义分析器和AI服务，针对Reddit用户内容进行分析
"""
import asyncio
import logging
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime
import sys
import os
import json

# 添加resona路径（用于数据模型）
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'resona'))

# 导入本地模块
from replicate_server import ReplicateAIService
from local_config import config
from resona.models import RedditUser, RedditPost, RedditComment, ParsedQuery
from pydantic import BaseModel, Field
from llm_prompt_manager import get_prompt_manager, PromptType

# 尝试导入resona模型，如果失败则使用本地模型
try:
    from resona.models import RedditUser, RedditPost, RedditComment, ParsedQuery
except ImportError:
    # 创建本地模型定义
    from typing import List, Dict, Any, Optional
    from pydantic import BaseModel, Field
    from datetime import datetime

    class RedditPost(BaseModel):
        id: str = ""
        text: str
        timestamp: datetime = Field(default_factory=datetime.now)
        subreddit: str = ""
        score: int = 0

    class RedditComment(BaseModel):
        id: str = ""
        text: str
        timestamp: datetime = Field(default_factory=datetime.now)
        subreddit: str = ""
        score: int = 0

    class RedditUser(BaseModel):
        username: str
        posts: List[RedditPost] = Field(default_factory=list)
        comments: List[RedditComment] = Field(default_factory=list)

    class ParsedQuery(BaseModel):
        original_text: str = Field(..., description="原始输入文本")
        search_intent: List[str] = Field(default_factory=list, description="Reddit搜索关键词")
        values_info: Dict[str, Any] = Field(default_factory=dict, description="初始价值观信息")
        emotional_state: Dict[str, float] = Field(default_factory=dict, description="情绪状态评分")
        topics: List[str] = Field(default_factory=list, description="主要话题")
        confidence: float = Field(default=1.0, description="解析置信度")

        # 新增深度分析字段
        core_concerns: List[str] = Field(default_factory=list, description="核心关切点")
        decision_points: List[str] = Field(default_factory=list, description="面临的决策点")
        life_domains: List[str] = Field(default_factory=list, description="涉及的生活领域")
        support_needs: List[str] = Field(default_factory=list, description="需要的支持类型")

logger = logging.getLogger(__name__)

class ProfileSemanticAnalyzer:
    """用户档案语义分析器 - 全面分析Reddit用户内容"""
    
    def __init__(self):
        """初始化语义分析器"""
        self.ai_service = ReplicateAIService()
        self.prompt_manager = get_prompt_manager()
        
        # 🎯 全面分析配置 - 确保所有内容都被分析
        self.max_content_length = 50000  # 大幅提升内容长度限制
        self.min_content_threshold = 100  # 最少内容阈值
        self.content_sample_size = 200  # 大幅提升采样大小
        
        # 新增：全面分析策略
        self.analysis_strategy = {
            "use_all_content": True,  # 使用所有内容
            "batch_analysis": True,   # 分批分析
            "comprehensive_prompt": True,  # 使用全面提示词
            "max_batch_size": 15000,  # 每批最大字符数
            "overlap_ratio": 0.1      # 批次间重叠比例
        }
        
        # 分析层级配置 - 确保深度分析
        self.analysis_layers = {
            "comprehensive_profile": {
                "enabled": True,
                "max_tokens": 4000,
                "prompt_template": "comprehensive_analysis_v2",
                "comprehensive_prompt": True,  # 使用全面提示词
            },
            "detailed_characteristics": {
                "enabled": True,
                "max_tokens": 6000,
                "content_ratio": 1.0,
                "priority": "deep_insights"
            },
            "behavioral_patterns": {
                "max_tokens": 5000,
                "content_ratio": 1.0,
                "priority": "pattern_recognition"
            }
        }
        
        logger.info("用户档案语义分析器初始化完成 - 全面分析版本")
    
    def _json_serializer(self, obj):
        """自定义JSON序列化器，处理datetime和Pydantic对象"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, 'dict'):  # Pydantic对象
            return obj.dict()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
    
    async def analyze_user_content(self, reddit_user: RedditUser) -> Dict[str, Any]:
        """
        分析Reddit用户内容 - 全面版本
        
        Args:
            reddit_user: Reddit用户数据
            
        Returns:
            Dict: 分析结果
        """
        logger.info(f"开始分析用户内容: {reddit_user.username}")
        
        try:
            # 1. 准备用户内容
            user_content = self._prepare_user_content(reddit_user)
            
            if not user_content or len(user_content) < self.min_content_threshold:
                return {
                    "success": False,
                    "error": "用户内容不足，无法进行有效分析",
                    "username": reddit_user.username,
                    "content_length": len(user_content) if user_content else 0
                }
            
            # 2. 生成内容摘要（保持更多信息）
            content_summary = self._generate_user_content_summary(user_content)
            
            # 3. 直接进行全面用户特征分析（合并语义分析）
            logger.info("开始全面用户特征分析...")
            
            # 使用所有内容进行一次性综合分析
            combined_analysis = await self._analyze_comprehensive_profile(user_content)
            
            # 解析综合分析结果
            parsed_query = combined_analysis["semantic_analysis"]
            user_characteristics = combined_analysis["user_characteristics"]
            
            # 5. 分析内容模式
            logger.info("开始内容模式分析...")
            content_patterns = await self._analyze_content_patterns(reddit_user)
            
            # 6. 生成综合分析报告
            analysis_report = self._generate_analysis_report(
                reddit_user.username,
                parsed_query,
                user_characteristics,
                content_patterns
            )
            
            logger.info(f"用户内容分析完成: {reddit_user.username}")
            
            return {
                "success": True,
                "username": reddit_user.username,
                "parsed_query": parsed_query,
                "user_characteristics": user_characteristics,
                "content_patterns": content_patterns,
                "analysis_report": analysis_report,
                "content_summary": content_summary,
                "stats": {
                    "content_length": len(user_content),
                    "posts_analyzed": len(reddit_user.posts),
                    "comments_analyzed": len(reddit_user.comments),
                    "confidence_score": parsed_query.confidence,
                    "analysis_strategy": "single_step_comprehensive"
                }
            }
            
        except Exception as e:
            logger.error(f"分析用户内容时发生错误: {e}")
            return {
                "success": False,
                "error": f"分析失败: {str(e)}",
                "username": reddit_user.username,
                "stats": {
                    "content_length": 0,
                    "posts_analyzed": 0,
                    "comments_analyzed": 0,
                    "confidence_score": 0.0,
                    "analysis_strategy": "failed"
                }
            }
    
    def _prepare_user_content(self, reddit_user: RedditUser) -> str:
        """
        准备用户内容进行分析 - 全面版本
        
        Args:
            reddit_user: Reddit用户数据
            
        Returns:
            str: 处理后的用户内容
        """
        # 按时间排序（最新的在前）
        all_posts_comments = reddit_user.posts + reddit_user.comments
        all_posts_comments.sort(key=lambda x: x.timestamp, reverse=True)
        
        # 🎯 全面内容选择 - 使用所有有效内容
        selected_content = self._comprehensive_content_selection(all_posts_comments)
        
        # 组织内容
        organized_content = []
        for item in selected_content:
            if isinstance(item, RedditPost) and len(item.text) > 30:  # 降低帖子长度阈值
                organized_content.append(f"[帖子 r/{item.subreddit}] {item.text}")
            elif isinstance(item, RedditComment) and len(item.text) > 20:  # 降低评论长度阈值
                organized_content.append(f"[评论 r/{item.subreddit}] {item.text}")
        
        # 合并所有内容
        combined_content = "\n\n".join(organized_content)
        
        # 如果内容过长，进行智能分批而不是截断
        if len(combined_content) > self.max_content_length:
            combined_content = self._smart_batch_content(combined_content)
        
        return combined_content
    
    def _comprehensive_content_selection(self, all_content: List) -> List:
        """
        全面内容选择 - 使用所有有效内容
        
        Args:
            all_content: 所有内容列表
            
        Returns:
            List: 选择的内容
        """
        if len(all_content) <= self.content_sample_size:
            return all_content
        
        # 策略1：按时间排序，优先选择最新内容
        sorted_content = sorted(all_content, key=lambda x: x.timestamp, reverse=True)
        
        # 策略2：按评分排序，确保高质量内容
        scored_content = []
        for item in sorted_content:
            score = getattr(item, 'score', 0)
            scored_content.append((item, score))
        
        # 策略3：智能选择 - 结合时间、评分和多样性
        selected = []
        
        # 选择前50%的高评分内容
        high_score_threshold = len(scored_content) // 2
        high_score_content = [item for item, score in scored_content[:high_score_threshold]]
        selected.extend(high_score_content)
        
        # 选择最新的内容（确保时间覆盖）
        recent_count = min(len(sorted_content) // 3, self.content_sample_size // 2)
        recent_content = sorted_content[:recent_count]
        selected.extend([item for item in recent_content if item not in selected])
        
        # 如果还不够，添加剩余内容
        remaining = [item for item in sorted_content if item not in selected]
        if remaining and len(selected) < self.content_sample_size:
            needed = self.content_sample_size - len(selected)
            selected.extend(remaining[:needed])
        
        # 去重并限制数量
        unique_selected = list(dict.fromkeys(selected))
        return unique_selected[:self.content_sample_size]
    
    def _smart_batch_content(self, content: str) -> str:
        """
        智能分批内容 - 保持内容完整性
        
        Args:
            content: 原始内容
            
        Returns:
            str: 分批后的内容
        """
        # 按段落分割
        paragraphs = content.split('\n\n')
        
        # 计算批次
        batch_size = self.analysis_strategy["max_batch_size"]
        overlap_size = int(batch_size * self.analysis_strategy["overlap_ratio"])
        
        if len(content) <= batch_size:
            return content
        
        # 创建重叠批次
        batches = []
        current_batch = []
        current_length = 0
        
        for paragraph in paragraphs:
            if current_length + len(paragraph) > batch_size:
                # 完成当前批次
                if current_batch:
                    batches.append('\n\n'.join(current_batch))
                
                # 开始新批次，包含重叠内容
                overlap_content = '\n\n'.join(current_batch[-3:])  # 保留最后3段作为重叠
                current_batch = [overlap_content, paragraph] if overlap_content else [paragraph]
                current_length = len(paragraph)
            else:
                current_batch.append(paragraph)
                current_length += len(paragraph)
        
        # 添加最后一个批次
        if current_batch:
            batches.append('\n\n'.join(current_batch))
        
        # 合并所有批次
        return "\n\n--- 批次分割 ---\n\n".join(batches)
    
    async def _analyze_comprehensive_profile(self, user_content: str) -> Dict[str, Any]:
        """
        全面分析用户画像 - 一次性完成语义分析和特征分析
        
        Args:
            user_content: 用户内容
            
        Returns:
            Dict: 包含语义分析和用户特征的综合结果
        """
        try:
            # 统计内容
            posts_count = user_content.count("[帖子")
            comments_count = user_content.count("[评论")
            logger.info(f"全面用户画像分析 - 拼接内容统计: {posts_count} 帖子, {comments_count} 评论")
            
            # 使用 Prompt Manager 生成 Prompt
            prompt_config = self.prompt_manager.get_full_prompt_config(
                PromptType.COMPREHENSIVE_USER_ANALYSIS,
                user_content=user_content,
                content_length=len(user_content)
            )
            
            logger.info(f"全面用户画像分析 Prompt 长度: {len(prompt_config['prompt'])}")
            
            # 调用AI服务
            ai_response = await self.ai_service.get_completion(
                prompt=prompt_config['prompt'],
                system_prompt=prompt_config['system_prompt'],
                temperature=prompt_config['temperature'],
                max_tokens=prompt_config['max_tokens']
            )
            logger.info(f"全面用户画像分析完成，AI响应长度: {len(ai_response)}")
            
            # 解析响应
            try:
                analysis_result = json.loads(self.ai_service._extract_json_from_response(ai_response))
                
                # 转换语义分析为ParsedQuery格式
                semantic_data = analysis_result["semantic_analysis"]
                parsed_query = ParsedQuery(
                    original_text=semantic_data.get("original_text", ""),
                    search_intent=semantic_data.get("search_intent", []),
                    values_info=semantic_data.get("values_info", {}),
                    emotional_state=semantic_data.get("emotional_state", {}),
                    topics=semantic_data.get("topics", []),
                    core_concerns=semantic_data.get("core_concerns", []),
                    decision_points=semantic_data.get("decision_points", []),
                    life_domains=semantic_data.get("life_domains", []),
                    support_needs=semantic_data.get("support_needs", []),
                    confidence=semantic_data.get("confidence", 0.9)
                )
                
                return {
                    "semantic_analysis": parsed_query,
                    "user_characteristics": analysis_result["user_characteristics"]
                }
                
            except json.JSONDecodeError as e:
                logger.error(f"解析AI响应JSON失败: {e}")
                # 返回默认结果
                return self._create_default_analysis_result()
                
        except Exception as e:
            logger.error(f"全面用户画像分析失败: {e}")
            return self._create_default_analysis_result()
    
    def _create_default_analysis_result(self) -> Dict[str, Any]:
        """创建默认分析结果"""
        default_parsed_query = ParsedQuery(
            original_text="分析失败",
            search_intent=[],
            values_info={},
            emotional_state={},
            topics=[],
            confidence=0.0
        )
        
        default_characteristics = {
            "personality_traits": {},
            "communication_style": {},
            "behavioral_patterns": {},
            "interests_and_expertise": {},
            "social_connections": {},
            "life_stage_indicators": {},
            "confidence_level": 0.0
        }
        
        return {
            "semantic_analysis": default_parsed_query,
            "user_characteristics": default_characteristics
        }

    def _generate_user_content_summary(self, content: str) -> str:
        """
        生成用户内容摘要 - 全面版本
        
        Args:
            content: 用户内容
            
        Returns:
            str: 内容摘要
        """
        # 🎯 不再过度压缩，保持更多原始信息
        if len(content) <= 10000:  # 如果内容不太长，直接使用
            return content
        
        # 对于超长内容，进行智能摘要
        lines = content.split('\n')
        important_lines = []
        
        for line in lines:
            if line.strip() and len(line.strip()) > 15:  # 降低行长度阈值
                important_lines.append(line.strip())
                
                # 大幅增加行数限制
                if len(important_lines) >= 100:  # 从15提升到100
                    break
        
        summary = '\n'.join(important_lines)
        
        # 提高摘要长度限制
        if len(summary) > 15000:  # 从2000提升到15000
            summary = summary[:15000] + "..."
        
        return summary

    async def _parse_user_content_with_ai_deprecated(self, content: str) -> ParsedQuery:
        """
        使用AI服务解析用户内容 - 全面版本

        Args:
            content: 用户内容摘要

        Returns:
            ParsedQuery: 解析结果
        """
        try:
            # 统计拼接内容帖子/评论数量
            posts_included = content.count("[帖子")
            comments_included = content.count("[评论")
            logger.debug("AI语义分析 - 拼接内容统计: %d 帖子, %d 评论", posts_included, comments_included)

            # 🎯 使用全面分析提示词
            prompt = f"""
请对以下Reddit用户的所有发言内容进行全面的深度分析。用户共有{posts_included}个帖子和{comments_included}个评论，请基于这些完整内容生成详细的用户画像。

用户完整内容：
{content}

请以JSON格式返回全面的分析结果：
{{
    "topics": ["主题1", "主题2", "主题3", "主题4", "主题5", "主题6", "主题7", "主题8"],
    "emotional_state": {{
        "joy": 0.2,
        "sadness": 0.1,
        "anger": 0.0,
        "fear": 0.3,
        "surprise": 0.1,
        "disgust": 0.0,
        "trust": 0.3,
        "anxiety": 0.2,
        "confusion": 0.1,
        "excitement": 0.1,
        "frustration": 0.1,
        "satisfaction": 0.2,
        "curiosity": 0.3,
        "empathy": 0.4
    }},
    "intent": "详细的用户意图和动机分析",
    "confidence": 0.85,
    "core_concerns": ["关切点1", "关切点2", "关切点3", "关切点4", "关切点5"],
    "decision_points": ["决策点1", "决策点2", "决策点3", "决策点4"],
    "life_domains": ["生活领域1", "生活领域2", "生活领域3", "生活领域4", "生活领域5"],
    "support_needs": ["支持需求1", "支持需求2", "支持需求3", "支持需求4"],
    "communication_style": "详细的沟通风格分析",
    "knowledge_areas": ["知识领域1", "知识领域2", "知识领域3", "知识领域4", "知识领域5"],
    "personality_insights": "基于所有内容的性格洞察",
    "behavioral_patterns": ["行为模式1", "行为模式2", "行为模式3", "行为模式4"],
    "value_system": ["价值观1", "价值观2", "价值观3", "价值观4", "价值观5"],
    "interests_and_passions": ["兴趣1", "兴趣2", "兴趣3", "兴趣4", "兴趣5", "兴趣6"],
    "social_dynamics": "社交动态和互动模式分析",
    "cognitive_style": "认知风格和思维方式分析",
    "emotional_intelligence": "情商和情绪管理能力分析",
    "life_goals": ["人生目标1", "人生目标2", "人生目标3"],
    "challenges_faced": ["面临的挑战1", "面临的挑战2", "面临的挑战3"],
    "strengths": ["优势1", "优势2", "优势3", "优势4"],
    "growth_areas": ["成长领域1", "成长领域2", "成长领域3"],
    "recommendation_areas": ["建议领域1", "建议领域2", "建议领域3", "建议领域4"]
}}

请基于用户的所有内容进行客观、全面、深入的分析，确保分析质量与内容量相匹配。避免过度推测，基于实际内容提供准确的洞察。
"""

            logger.debug("AI语义分析 Prompt 长度: %d", len(prompt))

            response = await self.ai_service.get_completion(
                prompt=prompt,
                max_tokens=self.analysis_layers["comprehensive_profile"]["max_tokens"],
                temperature=0.3,
                system_prompt="你是一个专业的心理分析师、社会学家和用户画像专家。请基于用户的所有Reddit发言内容进行全面深入的画像分析，确保分析质量与内容量相匹配。请严格按照JSON格式返回分析结果。"
            )

            logger.debug("AI语义分析 Response (前500字): %s", response[:500].replace("\n", " "))

            # 解析AI响应
            analysis_result = json.loads(self.ai_service._extract_json_from_response(response))

            # 构建ParsedQuery对象
            parsed_query = ParsedQuery(
                original_text=content,
                topics=analysis_result.get("topics", ["general"]),
                emotional_state=analysis_result.get("emotional_state", {"neutral": 0.5}),
                confidence=analysis_result.get("confidence", 0.5),
                core_concerns=analysis_result.get("core_concerns", ["寻求理解"]),
                decision_points=analysis_result.get("decision_points", ["人生选择"]),
                life_domains=analysis_result.get("life_domains", ["general"]),
                support_needs=analysis_result.get("support_needs", ["建议"])
            )

            return parsed_query

        except Exception as e:
            logger.error(f"AI语义分析失败: {e}")
            return ParsedQuery(
                original_text=content,
                topics=["general"],
                emotional_state={"neutral": 0.5},
                confidence=0.3,
                core_concerns=["寻求理解"],
                decision_points=["人生选择"],
                life_domains=["general"],
                support_needs=["建议"]
            )
    
    async def _analyze_user_characteristics_deprecated(self, content: str, parsed_query: ParsedQuery) -> Dict[str, Any]:
        """
        深度分析用户特征 - 全面版本
        
        Args:
            content: 用户内容
            parsed_query: 语义分析结果
            
        Returns:
            Dict: 用户特征分析
        """
        try:
            # 统计帖子/评论数量
            posts_included = content.count("[帖子")
            comments_included = content.count("[评论")
            logger.debug("用户特征分析 - 拼接内容统计: %d 帖子, %d 评论", posts_included, comments_included)

            # 🎯 使用所有内容进行深度分析
            prompt = f"""
请基于以下Reddit用户的所有内容进行全面的性格特征和用户画像分析。用户共有{posts_included}个帖子和{comments_included}个评论，请基于这些完整内容生成详细的用户特征分析。

用户完整内容：
{content}

请以JSON格式返回详细的用户特征分析：
{{
    "personality_traits": {{
        "openness": 0.7,
        "conscientiousness": 0.6,
        "extraversion": 0.4,
        "agreeableness": 0.8,
        "neuroticism": 0.3,
        "curiosity": 0.6,
        "empathy": 0.7,
        "analytical_thinking": 0.8,
        "creativity": 0.5,
        "persistence": 0.6,
        "adaptability": 0.7,
        "leadership": 0.4,
        "teamwork": 0.6,
        "independence": 0.8
    }},
    "communication_style": {{
        "formality": "casual|formal|mixed",
        "tone": "positive|negative|neutral|mixed",
        "verbosity": "concise|detailed|varies",
        "emotional_expression": "high|medium|low",
        "engagement_level": "high|medium|low",
        "response_style": "immediate|thoughtful|selective",
        "argumentation_style": "logical|emotional|balanced",
        "humor_usage": "frequent|occasional|rare",
        "empathy_expression": "high|medium|low",
        "assertiveness": "high|medium|low"
    }},
    "interests_and_values": {{
        "main_interests": ["兴趣1", "兴趣2", "兴趣3", "兴趣4", "兴趣5", "兴趣6", "兴趣7"],
        "core_values": ["价值观1", "价值观2", "价值观3", "价值观4", "价值观5", "价值观6"],
        "life_priorities": ["优先级1", "优先级2", "优先级3", "优先级4"],
        "passion_areas": ["热情领域1", "热情领域2", "热情领域3", "热情领域4", "热情领域5"],
        "learning_goals": ["学习目标1", "学习目标2", "学习目标3"],
        "career_aspirations": ["职业抱负1", "职业抱负2", "职业抱负3"]
    }},
    "behavioral_patterns": {{
        "posting_motivation": "help_seeking|knowledge_sharing|social_interaction|entertainment|self_expression|advocacy",
        "engagement_style": "active|passive|selective|responsive|proactive",
        "conflict_handling": "avoidant|confrontational|diplomatic|varies|constructive",
        "learning_style": "experiential|theoretical|social|independent|visual|auditory",
        "problem_solving": "analytical|intuitive|collaborative|independent|systematic|creative",
        "decision_making": "quick|deliberate|data_driven|intuitive|collaborative|independent",
        "stress_response": "fight|flight|freeze|adapt|seek_support",
        "social_interaction": "extroverted|introverted|ambivert|selective|networker"
    }},
    "psychological_indicators": {{
        "stress_level": "low|medium|high",
        "life_satisfaction": "low|medium|high",
        "social_connectivity": "isolated|somewhat_connected|well_connected|highly_connected",
        "future_orientation": "pessimistic|realistic|optimistic|very_optimistic",
        "self_confidence": "low|medium|high|very_high",
        "adaptability": "low|medium|high|very_high",
        "emotional_stability": "low|medium|high|very_high",
        "motivation_level": "low|medium|high|very_high",
        "resilience": "low|medium|high|very_high",
        "growth_mindset": "fixed|mixed|growth|strong_growth"
    }},
    "cognitive_abilities": {{
        "analytical_thinking": "low|medium|high|very_high",
        "creative_thinking": "low|medium|high|very_high",
        "critical_thinking": "low|medium|high|very_high",
        "problem_solving": "low|medium|high|very_high",
        "learning_ability": "low|medium|high|very_high",
        "memory": "low|medium|high|very_high",
        "attention_to_detail": "low|medium|high|very_high",
        "big_picture_thinking": "low|medium|high|very_high"
    }},
    "social_dynamics": {{
        "leadership_potential": "low|medium|high|very_high",
        "teamwork_ability": "low|medium|high|very_high",
        "mentoring_style": "directive|supportive|collaborative|hands_off",
        "conflict_resolution": "avoidant|accommodating|compromising|collaborating|competing",
        "influence_style": "authority|expertise|relationships|logic|emotion",
        "network_building": "low|medium|high|very_high",
        "community_contribution": "low|medium|high|very_high"
    }},
    "confidence_score": 0.85,
    "analysis_depth": "基于{posts_included}个帖子和{comments_included}个评论的全面深度分析",
    "content_coverage": "100%内容覆盖，无遗漏分析"
}}

请基于用户的所有内容进行客观、全面、深入的分析，确保分析质量与内容量相匹配。避免过度推测，基于实际内容提供准确的洞察。
"""
            
            logger.debug("用户特征分析 Prompt 长度: %d", len(prompt))

            response = await self.ai_service.get_completion(
                prompt=prompt,
                max_tokens=self.analysis_layers["detailed_characteristics"]["max_tokens"],
                temperature=0.3
            )
            
            # DEBUG: 输出用户特征分析 AI Response（前500字符）
            logger.debug("用户特征分析 Response (前500字): %s", response[:500].replace("\n", " "))

            # 解析AI响应
            analysis_result = json.loads(self.ai_service._extract_json_from_response(response))
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"用户特征分析失败: {e}")
            # 返回默认结构
            return {
                "personality_traits": {
                    "openness": 0.5,
                    "conscientiousness": 0.5,
                    "extraversion": 0.5,
                    "agreeableness": 0.5,
                    "neuroticism": 0.5
                },
                "communication_style": {
                    "formality": "mixed",
                    "tone": "neutral",
                    "verbosity": "varies",
                    "emotional_expression": "medium"
                },
                "interests_and_values": {
                    "main_interests": ["未知"],
                    "core_values": ["未知"],
                    "life_priorities": ["未知"]
                },
                "behavioral_patterns": {
                    "posting_motivation": "unknown",
                    "engagement_style": "unknown",
                    "conflict_handling": "unknown"
                },
                "psychological_indicators": {
                    "stress_level": "unknown",
                    "life_satisfaction": "unknown",
                    "social_connectivity": "unknown",
                    "future_orientation": "unknown"
                },
                "confidence_score": 0.3
            }
    
    async def _analyze_content_patterns(self, reddit_user: RedditUser) -> Dict[str, Any]:
        """
        分析内容模式
        
        Args:
            reddit_user: Reddit用户数据
            
        Returns:
            Dict: 内容模式分析
        """
        try:
            # 统计基本信息
            subreddit_distribution = {}
            post_lengths = []
            comment_lengths = []
            posting_times = []
            
            # 分析帖子
            for post in reddit_user.posts:
                subreddit = post.subreddit
                subreddit_distribution[subreddit] = subreddit_distribution.get(subreddit, 0) + 1
                post_lengths.append(len(post.text))
                posting_times.append(post.timestamp)
            
            # 分析评论
            for comment in reddit_user.comments:
                subreddit = comment.subreddit
                subreddit_distribution[subreddit] = subreddit_distribution.get(subreddit, 0) + 1
                comment_lengths.append(len(comment.text))
                posting_times.append(comment.timestamp)
            
            # 计算统计信息
            patterns = {
                "posting_frequency": {
                    "total_posts": len(reddit_user.posts),
                    "total_comments": len(reddit_user.comments),
                    "posts_to_comments_ratio": len(reddit_user.posts) / max(len(reddit_user.comments), 1)
                },
                "content_length": {
                    "avg_post_length": sum(post_lengths) / len(post_lengths) if post_lengths else 0,
                    "avg_comment_length": sum(comment_lengths) / len(comment_lengths) if comment_lengths else 0,
                    "max_post_length": max(post_lengths) if post_lengths else 0,
                    "max_comment_length": max(comment_lengths) if comment_lengths else 0
                },
                "subreddit_diversity": {
                    "unique_subreddits": len(subreddit_distribution),
                    "top_subreddits": sorted(subreddit_distribution.items(), key=lambda x: x[1], reverse=True)[:5],
                    "distribution": subreddit_distribution
                },
                "temporal_patterns": {
                    "date_range": {
                        "earliest": min(posting_times) if posting_times else None,
                        "latest": max(posting_times) if posting_times else None
                    },
                    "activity_span_days": (max(posting_times) - min(posting_times)).days if len(posting_times) > 1 else 0
                }
            }
            
            return patterns
            
        except Exception as e:
            logger.error(f"内容模式分析失败: {e}")
            return {
                "posting_frequency": {"total_posts": 0, "total_comments": 0},
                "content_length": {"avg_post_length": 0, "avg_comment_length": 0},
                "subreddit_diversity": {"unique_subreddits": 0, "top_subreddits": []},
                "temporal_patterns": {"date_range": {}, "activity_span_days": 0}
            }
    
    def _generate_analysis_report(self, username: str, parsed_query: ParsedQuery, 
                                user_characteristics: Dict[str, Any], 
                                content_patterns: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成综合分析报告
        
        Args:
            username: 用户名
            parsed_query: 语义分析结果
            user_characteristics: 用户特征
            content_patterns: 内容模式
            
        Returns:
            Dict: 综合分析报告
        """
        report = {
            "username": username,
            "analysis_timestamp": datetime.now(),
            "overall_assessment": {
                "confidence_level": (parsed_query.confidence + user_characteristics.get("confidence_score", 0.5)) / 2,
                "data_quality": self._assess_data_quality(content_patterns),
                "analysis_reliability": "high" if parsed_query.confidence > 0.7 else "medium" if parsed_query.confidence > 0.5 else "low"
            },
            "key_insights": {
                "dominant_emotions": self._extract_dominant_emotions(parsed_query.emotional_state),
                "primary_topics": parsed_query.topics[:3],
                "personality_summary": self._summarize_personality(user_characteristics.get("personality_traits", {})),
                "engagement_style": user_characteristics.get("behavioral_patterns", {}).get("engagement_style", "unknown")
            },
            "recommendations": {
                "communication_approach": self._suggest_communication_approach(user_characteristics),
                "potential_common_ground": self._identify_common_ground(parsed_query, user_characteristics),
                "interaction_tips": self._generate_interaction_tips(user_characteristics)
            }
        }
        
        return report
    
    def _assess_data_quality(self, content_patterns: Dict[str, Any]) -> str:
        """评估数据质量"""
        total_content = content_patterns.get("posting_frequency", {}).get("total_posts", 0) + \
                       content_patterns.get("posting_frequency", {}).get("total_comments", 0)
        
        if total_content >= 20:
            return "high"
        elif total_content >= 10:
            return "medium"
        else:
            return "low"
    
    def _extract_dominant_emotions(self, emotional_state: Dict[str, float]) -> List[str]:
        """提取主要情绪"""
        if not emotional_state:
            return ["neutral"]
        
        # 按情绪强度排序
        sorted_emotions = sorted(emotional_state.items(), key=lambda x: x[1], reverse=True)
        
        # 取前2-3个显著情绪
        dominant = []
        for emotion, intensity in sorted_emotions[:3]:
            if intensity > 0.3:  # 显著性阈值
                dominant.append(emotion)
        
        return dominant if dominant else ["neutral"]
    
    def _summarize_personality(self, personality_traits: Dict[str, float]) -> str:
        """总结性格特征"""
        if not personality_traits:
            return "性格特征不明确"
        
        # 找出最突出的特征
        max_trait = max(personality_traits.items(), key=lambda x: x[1])
        min_trait = min(personality_traits.items(), key=lambda x: x[1])
        
        trait_descriptions = {
            "openness": "开放性",
            "conscientiousness": "责任感",
            "extraversion": "外向性",
            "agreeableness": "亲和性",
            "neuroticism": "神经质"
        }
        
        return f"偏向{trait_descriptions.get(max_trait[0], max_trait[0])}，较少{trait_descriptions.get(min_trait[0], min_trait[0])}"
    
    def _suggest_communication_approach(self, user_characteristics: Dict[str, Any]) -> str:
        """建议沟通方式"""
        comm_style = user_characteristics.get("communication_style", {})
        behavioral = user_characteristics.get("behavioral_patterns", {})
        
        if comm_style.get("formality") == "formal":
            return "使用正式、专业的语言"
        elif comm_style.get("emotional_expression") == "high":
            return "可以使用情感丰富的表达方式"
        elif behavioral.get("engagement_style") == "passive":
            return "采用温和、鼓励性的语调"
        else:
            return "保持友好、自然的沟通风格"
    
    def _identify_common_ground(self, parsed_query: ParsedQuery, user_characteristics: Dict[str, Any]) -> List[str]:
        """识别共同点"""
        common_ground = []
        
        # 从话题中提取
        if parsed_query.topics:
            common_ground.extend(parsed_query.topics[:2])
        
        # 从兴趣中提取
        interests = user_characteristics.get("interests_and_values", {}).get("main_interests", [])
        if interests:
            common_ground.extend(interests[:2])
        
        return common_ground[:3] if common_ground else ["生活经验分享"]
    
    def _generate_interaction_tips(self, user_characteristics: Dict[str, Any]) -> List[str]:
        """生成交互建议"""
        tips = []
        
        psych_indicators = user_characteristics.get("psychological_indicators", {})
        behavioral = user_characteristics.get("behavioral_patterns", {})
        
        if psych_indicators.get("stress_level") == "high":
            tips.append("注意提供情感支持")
        
        if behavioral.get("posting_motivation") == "help_seeking":
            tips.append("可以分享相关经验和建议")
        
        if psych_indicators.get("social_connectivity") == "isolated":
            tips.append("采用包容、理解的态度")
        
        return tips if tips else ["保持开放和友好的交流态度"]
    
    async def analyze_comment_reasoning(self, comment_text: str, user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于用户画像分析特定评论的理由和动机
        
        Args:
            comment_text: 要分析的评论内容
            user_profile: 用户画像分析结果
            
        Returns:
            Dict: 评论理由分析结果
        """
        try:
            # 提取用户画像关键信息
            personality_traits = user_profile.get('user_characteristics', {}).get('personality_traits', {})
            behavioral_patterns = user_profile.get('user_characteristics', {}).get('behavioral_patterns', {})
            communication_style = user_profile.get('user_characteristics', {}).get('communication_style', {})
            interests_values = user_profile.get('user_characteristics', {}).get('interests_and_values', {})
            psychological_indicators = user_profile.get('user_characteristics', {}).get('psychological_indicators', {})
            
            # 兼容 dict / ParsedQuery 两种格式
            parsed_query_raw = user_profile.get('parsed_query', {})
            if isinstance(parsed_query_raw, dict):
                parsed_query_data = parsed_query_raw
            else:
                try:
                    parsed_query_data = parsed_query_raw.dict()
                except AttributeError:
                    parsed_query_data = {}

            core_concerns = parsed_query_data.get('core_concerns', [])
            emotional_state = parsed_query_data.get('emotional_state', {})

            # 使用 Prompt Manager 生成 Prompt
            prompt_config = self.prompt_manager.get_full_prompt_config(
                PromptType.COMMENT_REASONING_ANALYSIS,
                user_profile=json.dumps(user_profile, indent=2, ensure_ascii=False, default=self._json_serializer),
                comment_text=comment_text
            )

            logger.debug("评论推理分析 Prompt 长度: %d", len(prompt_config['prompt']))

            # 调用AI服务
            response = await self.ai_service.get_completion(
                prompt=prompt_config['prompt'],
                max_tokens=prompt_config['max_tokens'],
                temperature=prompt_config['temperature'],
                system_prompt=prompt_config['system_prompt']
            )

            # 解析AI响应
            analysis_result = json.loads(self.ai_service._extract_json_from_response(response))
            
            logger.info(f"评论推理分析完成, 主要动机: {analysis_result.get('reasoning_analysis', {}).get('primary_motivation')}")
            return analysis_result

        except Exception as e:
            logger.error(f"评论推理分析失败: {e}")
            return {"error": str(e), "reasoning_analysis": {"primary_motivation": "分析失败"}}
    
    async def close(self):
        """关闭服务（清理资源）"""
        try:
            await self.ai_service.close()
            logger.info("语义分析器已关闭")
        except Exception as e:
                          logger.error(f"关闭语义分析器时发生错误: {e}")

# 便捷函数
async def analyze_reddit_user(reddit_user: RedditUser) -> Dict[str, Any]:
    """
    便捷函数：分析Reddit用户
    
    Args:
        reddit_user: Reddit用户数据
        
    Returns:
        Dict: 分析结果
    """
    analyzer = ProfileSemanticAnalyzer()
    try:
        result = await analyzer.analyze_user_content(reddit_user)
        return result
    finally:
        await analyzer.close()

if __name__ == "__main__":
    # 测试代码
    from resona.models import RedditPost, RedditComment
    
    async def test_analyzer():
        # 创建测试用户数据
        test_posts = [
            RedditPost(
                id="test1",
                text="I've been struggling with career decisions lately. Not sure if I should continue my current job or pursue further education.",
                timestamp=datetime.now(),
                subreddit="careerguidance",
                score=5
            )
        ]
        
        test_comments = [
            RedditComment(
                id="test2",
                text="This is really helpful advice. I've been in a similar situation and found that taking time to reflect on your values really helps.",
                timestamp=datetime.now(),
                subreddit="advice",
                score=3
            )
        ]
        
        test_user = RedditUser(
            username="testuser",
            posts=test_posts,
            comments=test_comments
        )
        
        analyzer = ProfileSemanticAnalyzer()
        
        try:
            result = await analyzer.analyze_user_content(test_user)
            print(f"分析结果: {result['success']}")
            if result['success']:
                print(f"置信度: {result['stats']['confidence_score']:.3f}")
                print(f"主要话题: {result['parsed_query'].topics}")
                print(f"情绪状态: {result['parsed_query'].emotional_state}")
            else:
                print(f"分析失败: {result['error']}")
                
        finally:
            await analyzer.close()
    
    # 运行测试
    # asyncio.run(test_analyzer()) 