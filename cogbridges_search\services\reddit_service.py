"""
CogBridges Search - Reddit服务
实现Reddit API调用，支持代理，获取帖子内容和评论数据
"""

import os
import re
import asyncio
import time
from typing import List, Optional, Dict, Any, Tuple
from urllib.parse import urlparse, parse_qs
import praw
import asyncpraw
from aiohttp import ClientSession
from tenacity import retry, stop_after_attempt, wait_exponential

from ..config import config
from ..models.reddit_models import RedditPost, RedditComment, RedditUser, UserHistory
from ..utils.logger_utils import get_logger, log_api_call, log_reddit_operation
from ..utils.proxy_utils import setup_proxy


class RedditService:
    """Reddit服务类"""
    
    def __init__(self):
        """初始化Reddit服务"""
        self.logger = get_logger(__name__)
        
        # 验证配置
        if not config.reddit_configured:
            self.logger.error("Reddit API未配置")
            raise ValueError("Reddit API配置不完整")
        
        # 设置代理
        self.proxy_dict = setup_proxy()
        if self.proxy_dict:
            # 设置环境变量，PRAW会自动使用
            if "http" in self.proxy_dict:
                os.environ["HTTP_PROXY"] = self.proxy_dict["http"]
            if "https" in self.proxy_dict:
                os.environ["HTTPS_PROXY"] = self.proxy_dict["https"]
        
        # 初始化同步Reddit客户端
        self.reddit = praw.Reddit(
            client_id=config.REDDIT_CLIENT_ID,
            client_secret=config.REDDIT_CLIENT_SECRET,
            user_agent=config.REDDIT_USER_AGENT
        )
        self.reddit.read_only = True
        
        # 异步Reddit客户端（延迟初始化）
        self.async_reddit = None
        self._async_initialized = False
        
        # 请求统计
        self.request_count = 0
        self.total_request_time = 0.0
        
        self.logger.info("Reddit服务初始化成功")
    
    async def _ensure_async_reddit(self):
        """确保异步Reddit客户端已初始化"""
        if not self._async_initialized:
            # 创建支持代理的ClientSession
            session_kwargs = {"trust_env": True}
            if self.proxy_dict:
                session_kwargs["connector"] = None  # 让aiohttp使用环境变量中的代理
            
            session = ClientSession(**session_kwargs)
            
            self.async_reddit = asyncpraw.Reddit(
                client_id=config.REDDIT_CLIENT_ID,
                client_secret=config.REDDIT_CLIENT_SECRET,
                user_agent=config.REDDIT_USER_AGENT,
                requestor_kwargs={"session": session}
            )
            self.async_reddit.read_only = True
            self._async_initialized = True
            
            self.logger.debug("异步Reddit客户端初始化完成")
        
        return self.async_reddit
    
    def parse_reddit_url(self, url: str) -> Optional[Dict[str, str]]:
        """
        解析Reddit URL，提取相关信息
        
        Args:
            url: Reddit URL
            
        Returns:
            解析结果字典，包含类型和相关信息
        """
        try:
            parsed = urlparse(url)
            path = parsed.path
            
            # 匹配帖子URL: /r/subreddit/comments/post_id/title/
            post_pattern = r'/r/([^/]+)/comments/([^/]+)'
            match = re.search(post_pattern, path)
            if match:
                return {
                    "type": "post",
                    "subreddit": match.group(1),
                    "post_id": match.group(2),
                    "url": url
                }
            
            # 匹配用户URL: /u/username 或 /user/username
            user_pattern = r'/u(?:ser)?/([^/]+)'
            match = re.search(user_pattern, path)
            if match:
                return {
                    "type": "user", 
                    "username": match.group(1),
                    "url": url
                }
            
            # 匹配子版块URL: /r/subreddit
            subreddit_pattern = r'/r/([^/]+)/?$'
            match = re.search(subreddit_pattern, path)
            if match:
                return {
                    "type": "subreddit",
                    "subreddit": match.group(1),
                    "url": url
                }
            
            return {"type": "unknown", "url": url}
            
        except Exception as e:
            self.logger.error(f"解析Reddit URL失败: {e}")
            return None
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    @log_api_call("Reddit", "submission", "GET")
    async def get_post_details(self, post_url: str) -> Optional[RedditPost]:
        """
        获取Reddit帖子详细信息
        
        Args:
            post_url: Reddit帖子URL
            
        Returns:
            Reddit帖子对象
        """
        start_time = time.time()
        
        try:
            # 解析URL获取帖子ID
            url_info = self.parse_reddit_url(post_url)
            if not url_info or url_info["type"] != "post":
                self.logger.error(f"无效的Reddit帖子URL: {post_url}")
                return None
            
            post_id = url_info["post_id"]
            
            # 获取异步Reddit客户端
            reddit = await self._ensure_async_reddit()
            
            # 获取帖子
            submission = await reddit.submission(id=post_id)
            await submission.load()
            
            # 创建RedditPost对象
            post = RedditPost(
                id=submission.id,
                title=submission.title,
                selftext=submission.selftext or "",
                author=submission.author.name if submission.author else "[deleted]",
                score=submission.score,
                upvote_ratio=submission.upvote_ratio,
                num_comments=submission.num_comments,
                created_utc=submission.created_utc,
                subreddit=submission.subreddit.display_name,
                permalink=submission.permalink,
                url=submission.url,
                is_self=submission.is_self,
                gilded=getattr(submission, 'gilded', 0),
                pinned=submission.pinned,
                locked=submission.locked,
                archived=submission.archived,
                over_18=submission.over_18
            )
            
            # 更新统计
            self.request_count += 1
            self.total_request_time += time.time() - start_time
            
            # 记录操作日志
            log_reddit_operation("get_post", {
                "post_id": post_id,
                "subreddit": post.subreddit,
                "title": post.title[:50],
                "score": post.score,
                "num_comments": post.num_comments
            })
            
            self.logger.info(f"获取帖子成功: {post.title[:50]}...")
            return post
            
        except Exception as e:
            self.logger.error(f"获取Reddit帖子失败: {e}")
            return None

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    @log_api_call("Reddit", "comments", "GET")
    async def get_post_comments(
        self,
        post_url: str,
        limit: int = None,
        sort: str = "top"
    ) -> List[RedditComment]:
        """
        获取Reddit帖子的评论

        Args:
            post_url: Reddit帖子URL
            limit: 评论数量限制
            sort: 排序方式 (top, best, new, controversial)

        Returns:
            Reddit评论列表
        """
        start_time = time.time()
        limit = limit or config.REDDIT_TOP_COMMENTS_COUNT

        try:
            # 解析URL获取帖子ID
            url_info = self.parse_reddit_url(post_url)
            if not url_info or url_info["type"] != "post":
                self.logger.error(f"无效的Reddit帖子URL: {post_url}")
                return []

            post_id = url_info["post_id"]

            # 获取异步Reddit客户端
            reddit = await self._ensure_async_reddit()

            # 获取帖子和评论
            submission = await reddit.submission(id=post_id)
            await submission.load()

            # 设置评论排序
            if sort == "top":
                submission.comment_sort = "top"
            elif sort == "best":
                submission.comment_sort = "best"
            elif sort == "new":
                submission.comment_sort = "new"
            elif sort == "controversial":
                submission.comment_sort = "controversial"

            # 展开评论树（限制深度避免过度加载）
            await submission.comments.replace_more(limit=3)

            comments = []
            comment_count = 0

            # 遍历顶级评论
            for comment in submission.comments:
                if comment_count >= limit:
                    break

                if hasattr(comment, 'body') and comment.body != '[deleted]':
                    try:
                        reddit_comment = RedditComment(
                            id=comment.id,
                            body=comment.body,
                            author=comment.author.name if comment.author else "[deleted]",
                            score=comment.score,
                            created_utc=comment.created_utc,
                            parent_id=comment.parent_id,
                            subreddit=comment.subreddit.display_name,
                            permalink=comment.permalink,
                            is_submitter=comment.is_submitter,
                            gilded=getattr(comment, 'gilded', 0),
                            replies_count=len(comment.replies) if hasattr(comment, 'replies') else 0
                        )

                        comments.append(reddit_comment)
                        comment_count += 1

                    except Exception as e:
                        self.logger.warning(f"解析评论失败: {e}")
                        continue

            # 更新统计
            self.request_count += 1
            self.total_request_time += time.time() - start_time

            # 记录操作日志
            log_reddit_operation("get_comments", {
                "post_id": post_id,
                "comments_count": len(comments),
                "sort": sort,
                "limit": limit
            })

            self.logger.info(f"获取评论成功: {len(comments)} 条评论")
            return comments

        except Exception as e:
            self.logger.error(f"获取Reddit评论失败: {e}")
            return []

    async def get_post_with_comments(
        self,
        post_url: str,
        comments_limit: int = None
    ) -> Tuple[Optional[RedditPost], List[RedditComment]]:
        """
        同时获取帖子和评论

        Args:
            post_url: Reddit帖子URL
            comments_limit: 评论数量限制

        Returns:
            (帖子对象, 评论列表) 元组
        """
        self.logger.info(f"获取帖子和评论: {post_url}")

        # 并行获取帖子和评论
        post_task = self.get_post_details(post_url)
        comments_task = self.get_post_comments(post_url, comments_limit)

        post, comments = await asyncio.gather(post_task, comments_task)

        return post, comments

    def extract_commenters(self, comments: List[RedditComment]) -> List[str]:
        """
        从评论列表中提取评论者用户名

        Args:
            comments: Reddit评论列表

        Returns:
            去重的用户名列表
        """
        commenters = set()

        for comment in comments:
            if comment.author and comment.author != "[deleted]":
                commenters.add(comment.author)

        return list(commenters)

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    @log_api_call("Reddit", "user", "GET")
    async def get_user_info(self, username: str) -> Optional[RedditUser]:
        """
        获取Reddit用户基本信息

        Args:
            username: 用户名

        Returns:
            Reddit用户对象
        """
        try:
            # 获取异步Reddit客户端
            reddit = await self._ensure_async_reddit()

            # 获取用户信息
            redditor = await reddit.redditor(username)
            await redditor.load()

            user = RedditUser(
                username=redditor.name,
                created_utc=redditor.created_utc,
                comment_karma=redditor.comment_karma,
                link_karma=redditor.link_karma,
                is_gold=getattr(redditor, 'is_gold', False),
                is_mod=getattr(redditor, 'is_mod', False),
                has_verified_email=getattr(redditor, 'has_verified_email', False)
            )

            self.logger.debug(f"获取用户信息成功: {username}")
            return user

        except Exception as e:
            self.logger.warning(f"获取用户信息失败 {username}: {e}")
            return None

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    @log_api_call("Reddit", "user_history", "GET")
    async def get_user_history(
        self,
        username: str,
        subreddit: str = None,
        posts_limit: int = None,
        comments_limit: int = None
    ) -> Optional[UserHistory]:
        """
        获取用户历史数据

        Args:
            username: 用户名
            subreddit: 指定子版块（可选）
            posts_limit: 帖子数量限制
            comments_limit: 评论数量限制

        Returns:
            用户历史数据对象
        """
        start_time = time.time()
        posts_limit = posts_limit or config.USER_HISTORY_POSTS_COUNT
        comments_limit = comments_limit or config.USER_HISTORY_COMMENTS_COUNT

        try:
            # 获取用户基本信息
            user_info = await self.get_user_info(username)
            if not user_info:
                return None

            # 获取异步Reddit客户端
            reddit = await self._ensure_async_reddit()
            redditor = await reddit.redditor(username)

            posts = []
            comments = []
            subreddit_activity = {}

            # 获取用户帖子
            try:
                post_count = 0
                async for submission in redditor.submissions.new(limit=posts_limit * 2):  # 多获取一些以便过滤
                    if post_count >= posts_limit:
                        break

                    # 如果指定了子版块，只获取该子版块的内容
                    if subreddit and submission.subreddit.display_name.lower() != subreddit.lower():
                        continue

                    # 只获取有内容的帖子
                    if submission.selftext and len(submission.selftext) > 20:
                        try:
                            post = RedditPost(
                                id=submission.id,
                                title=submission.title,
                                selftext=submission.selftext,
                                author=submission.author.name if submission.author else "[deleted]",
                                score=submission.score,
                                upvote_ratio=submission.upvote_ratio,
                                num_comments=submission.num_comments,
                                created_utc=submission.created_utc,
                                subreddit=submission.subreddit.display_name,
                                permalink=submission.permalink,
                                url=submission.url,
                                is_self=submission.is_self,
                                gilded=getattr(submission, 'gilded', 0),
                                pinned=submission.pinned,
                                locked=submission.locked,
                                archived=submission.archived,
                                over_18=submission.over_18
                            )

                            posts.append(post)
                            post_count += 1

                            # 统计子版块活动
                            sub_name = post.subreddit
                            subreddit_activity[sub_name] = subreddit_activity.get(sub_name, 0) + 1

                        except Exception as e:
                            self.logger.warning(f"解析用户帖子失败: {e}")
                            continue

            except Exception as e:
                self.logger.warning(f"获取用户帖子失败 {username}: {e}")

            # 获取用户评论
            try:
                comment_count = 0
                async for comment in redditor.comments.new(limit=comments_limit * 2):  # 多获取一些以便过滤
                    if comment_count >= comments_limit:
                        break

                    # 如果指定了子版块，只获取该子版块的内容
                    if subreddit and comment.subreddit.display_name.lower() != subreddit.lower():
                        continue

                    # 过滤掉删除的评论和太短的评论
                    if comment.body and comment.body != '[deleted]' and len(comment.body) > 10:
                        try:
                            reddit_comment = RedditComment(
                                id=comment.id,
                                body=comment.body,
                                author=comment.author.name if comment.author else "[deleted]",
                                score=comment.score,
                                created_utc=comment.created_utc,
                                parent_id=comment.parent_id,
                                subreddit=comment.subreddit.display_name,
                                permalink=comment.permalink,
                                is_submitter=comment.is_submitter,
                                gilded=getattr(comment, 'gilded', 0)
                            )

                            comments.append(reddit_comment)
                            comment_count += 1

                            # 统计子版块活动
                            sub_name = reddit_comment.subreddit
                            subreddit_activity[sub_name] = subreddit_activity.get(sub_name, 0) + 1

                        except Exception as e:
                            self.logger.warning(f"解析用户评论失败: {e}")
                            continue

            except Exception as e:
                self.logger.warning(f"获取用户评论失败 {username}: {e}")

            # 创建用户历史对象
            user_history = UserHistory(
                user=user_info,
                posts=posts,
                comments=comments,
                subreddit_activity=subreddit_activity
            )

            # 更新统计
            self.request_count += 1
            self.total_request_time += time.time() - start_time

            # 记录操作日志
            log_reddit_operation("get_user_history", {
                "username": username,
                "subreddit": subreddit,
                "posts_count": len(posts),
                "comments_count": len(comments),
                "subreddit_activity": len(subreddit_activity)
            })

            self.logger.info(f"获取用户历史成功 {username}: {len(posts)}帖子, {len(comments)}评论")
            return user_history

        except Exception as e:
            self.logger.error(f"获取用户历史失败 {username}: {e}")
            return None

    async def get_multiple_user_histories(
        self,
        usernames: List[str],
        subreddit: str = None,
        posts_limit: int = None,
        comments_limit: int = None
    ) -> Dict[str, Optional[UserHistory]]:
        """
        并行获取多个用户的历史数据

        Args:
            usernames: 用户名列表
            subreddit: 指定子版块（可选）
            posts_limit: 帖子数量限制
            comments_limit: 评论数量限制

        Returns:
            用户名到用户历史的映射字典
        """
        self.logger.info(f"开始并行获取 {len(usernames)} 个用户的历史数据")

        # 创建并发任务
        tasks = []
        for username in usernames:
            task = self.get_user_history(username, subreddit, posts_limit, comments_limit)
            tasks.append(task)

        # 并行执行，使用信号量限制并发数
        semaphore = asyncio.Semaphore(config.MAX_CONCURRENT_REQUESTS)

        async def limited_task(task, username):
            async with semaphore:
                try:
                    result = await task
                    return username, result
                except Exception as e:
                    self.logger.error(f"获取用户历史失败 {username}: {e}")
                    return username, None

        # 执行所有任务
        results = await asyncio.gather(*[
            limited_task(task, username)
            for task, username in zip(tasks, usernames)
        ])

        # 构建结果字典
        user_histories = {}
        successful_count = 0

        for username, history in results:
            user_histories[username] = history
            if history:
                successful_count += 1

        self.logger.info(f"用户历史获取完成: {successful_count}/{len(usernames)} 成功")
        return user_histories

    async def process_search_results(
        self,
        post_urls: List[str],
        comments_limit: int = None
    ) -> Dict[str, Any]:
        """
        处理搜索结果，获取帖子内容、评论和用户历史

        Args:
            post_urls: Reddit帖子URL列表
            comments_limit: 每个帖子的评论数量限制

        Returns:
            完整的处理结果字典
        """
        self.logger.info(f"开始处理 {len(post_urls)} 个搜索结果")
        start_time = time.time()

        # 第一阶段：并行获取所有帖子和评论
        self.logger.info("第一阶段：获取帖子和评论")
        post_tasks = []
        for url in post_urls:
            task = self.get_post_with_comments(url, comments_limit)
            post_tasks.append(task)

        post_results = await asyncio.gather(*post_tasks, return_exceptions=True)

        # 整理第一阶段结果
        posts_data = []
        all_commenters = set()

        for i, result in enumerate(post_results):
            if isinstance(result, Exception):
                self.logger.error(f"获取帖子失败 {post_urls[i]}: {result}")
                continue

            post, comments = result
            if post:
                post_data = {
                    "post": post,
                    "comments": comments,
                    "url": post_urls[i]
                }
                posts_data.append(post_data)

                # 收集评论者
                commenters = self.extract_commenters(comments)
                all_commenters.update(commenters)

        self.logger.info(f"第一阶段完成: {len(posts_data)} 个帖子, {len(all_commenters)} 个评论者")

        # 第二阶段：并行获取所有评论者的历史数据
        if all_commenters:
            self.logger.info("第二阶段：获取评论者历史数据")

            # 从帖子中提取子版块信息，用于过滤用户历史
            subreddits = set()
            for post_data in posts_data:
                subreddits.add(post_data["post"].subreddit)

            # 为每个子版块获取用户历史
            user_histories = {}
            for subreddit in subreddits:
                self.logger.info(f"获取子版块 r/{subreddit} 的用户历史")
                subreddit_histories = await self.get_multiple_user_histories(
                    list(all_commenters),
                    subreddit=subreddit
                )

                # 合并结果
                for username, history in subreddit_histories.items():
                    if history:
                        if username not in user_histories:
                            user_histories[username] = {}
                        user_histories[username][subreddit] = history
        else:
            user_histories = {}

        # 构建最终结果
        processing_time = time.time() - start_time

        result = {
            "posts_data": posts_data,
            "user_histories": user_histories,
            "statistics": {
                "total_posts": len(posts_data),
                "total_commenters": len(all_commenters),
                "total_user_histories": len(user_histories),
                "processing_time": processing_time,
                "subreddits": list(subreddits) if 'subreddits' in locals() else []
            },
            "success": True,
            "timestamp": time.time()
        }

        self.logger.info(f"搜索结果处理完成，耗时 {processing_time:.2f}秒")
        return result

    def test_api_connection(self) -> bool:
        """
        测试Reddit API连接

        Returns:
            连接是否成功
        """
        try:
            # 测试同步客户端
            test_subreddit = self.reddit.subreddit("test")
            test_subreddit.display_name  # 触发API调用

            self.logger.info("Reddit API连接测试成功")
            return True

        except Exception as e:
            self.logger.error(f"Reddit API连接测试失败: {e}")
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取服务统计信息

        Returns:
            统计信息字典
        """
        avg_request_time = (
            self.total_request_time / self.request_count
            if self.request_count > 0 else 0
        )

        return {
            "request_count": self.request_count,
            "total_request_time": self.total_request_time,
            "average_request_time": avg_request_time,
            "api_configured": config.reddit_configured,
            "proxy_configured": config.proxy_configured,
            "comments_per_post": config.REDDIT_TOP_COMMENTS_COUNT,
            "user_history_posts": config.USER_HISTORY_POSTS_COUNT,
            "user_history_comments": config.USER_HISTORY_COMMENTS_COUNT
        }


if __name__ == "__main__":
    # 测试Reddit服务
    import asyncio

    async def test_reddit_service():
        print("CogBridges Search - Reddit服务测试")
        print("=" * 50)

        try:
            # 创建Reddit服务
            reddit_service = RedditService()

            # 测试API连接
            print("🔗 测试API连接...")
            if reddit_service.test_api_connection():
                print("✅ API连接正常")
            else:
                print("❌ API连接失败")
                return

            # 测试URL解析
            print("\n🔍 测试URL解析...")
            test_urls = [
                "https://www.reddit.com/r/Python/comments/abc123/test_post/",
                "https://www.reddit.com/u/testuser",
                "https://www.reddit.com/r/Python/"
            ]

            for url in test_urls:
                parsed = reddit_service.parse_reddit_url(url)
                print(f"  {url} -> {parsed}")

            # 测试获取帖子（使用一个真实的Reddit帖子URL进行测试）
            print("\n📝 测试获取帖子...")
            test_post_url = "https://www.reddit.com/r/test/comments/1/"  # 使用测试子版块

            try:
                post = await reddit_service.get_post_details(test_post_url)
                if post:
                    print(f"✅ 获取帖子成功: {post.title[:50]}...")
                    print(f"   作者: {post.author}")
                    print(f"   分数: {post.score}")
                    print(f"   评论数: {post.num_comments}")
                else:
                    print("⚠️ 未找到帖子（可能是测试URL无效）")
            except Exception as e:
                print(f"⚠️ 获取帖子测试跳过: {e}")

            # 显示统计信息
            print("\n📊 服务统计:")
            stats = reddit_service.get_statistics()
            for key, value in stats.items():
                print(f"  {key}: {value}")

        except Exception as e:
            print(f"❌ 测试失败: {e}")

    # 运行测试
    asyncio.run(test_reddit_service())
