/**
 * CogBridges Search - 前端JavaScript应用
 * Silicon Valley风格的现代化交互体验
 */

class CogBridgesApp {
    constructor() {
        this.currentSession = null;
        this.searchInProgress = false;
        this.currentResults = null;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setupSearchInput();
        this.setupTabs();
        this.loadRecentSessions();
    }
    
    bindEvents() {
        // 搜索表单提交
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performSearch();
            });
        }
        
        // 搜索按钮
        const searchButton = document.getElementById('searchButton');
        if (searchButton) {
            searchButton.addEventListener('click', () => this.performSearch());
        }
        
        // 手气不错按钮
        const luckyButton = document.getElementById('luckyButton');
        if (luckyButton) {
            luckyButton.addEventListener('click', () => this.feelingLucky());
        }
        
        // 清除按钮
        const clearButton = document.getElementById('clearButton');
        if (clearButton) {
            clearButton.addEventListener('click', () => this.clearSearch());
        }
        
        // 新搜索按钮
        const newSearchButton = document.getElementById('newSearchButton');
        if (newSearchButton) {
            newSearchButton.addEventListener('click', () => this.newSearch());
        }
        
        // 导出按钮
        const exportButton = document.getElementById('exportButton');
        if (exportButton) {
            exportButton.addEventListener('click', () => this.exportResults());
        }
        
        // 重试按钮
        const retryButton = document.getElementById('retryButton');
        if (retryButton) {
            retryButton.addEventListener('click', () => this.performSearch());
        }
        
        // 返回按钮
        const backButton = document.getElementById('backButton');
        if (backButton) {
            backButton.addEventListener('click', () => this.showSearchSection());
        }
    }
    
    setupSearchInput() {
        const searchInput = document.getElementById('searchInput');
        if (!searchInput) return;
        
        // 实时搜索建议
        let suggestionTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(suggestionTimeout);
            const query = e.target.value.trim();
            
            if (query.length > 2) {
                suggestionTimeout = setTimeout(() => {
                    this.fetchSearchSuggestions(query);
                }, 300);
            } else {
                this.hideSuggestions();
            }
        });
        
        // 键盘导航
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.performSearch();
            } else if (e.key === 'Escape') {
                this.hideSuggestions();
            }
        });
        
        // 失去焦点时隐藏建议
        searchInput.addEventListener('blur', () => {
            setTimeout(() => this.hideSuggestions(), 200);
        });
    }
    
    setupTabs() {
        const tabButtons = document.querySelectorAll('.tab-button');
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.getAttribute('data-tab');
                this.switchTab(tabName);
            });
        });
    }
    
    async performSearch() {
        if (this.searchInProgress) return;
        
        const searchInput = document.getElementById('searchInput');
        const query = searchInput.value.trim();
        
        if (!query) {
            this.showError('请输入搜索查询');
            return;
        }
        
        if (query.length < 3) {
            this.showError('搜索查询至少需要3个字符');
            return;
        }
        
        this.searchInProgress = true;
        this.showLoadingSection();
        this.updateProgress(0, '开始搜索...');
        
        try {
            // 获取搜索参数
            const resultsCount = document.getElementById('resultsCount').value;
            const redditOnly = document.getElementById('redditOnly').checked;
            
            const searchParams = {
                query: query,
                max_results: parseInt(resultsCount),
                reddit_only: redditOnly
            };
            
            // 第一阶段：Google搜索
            this.updateProgress(25, '搜索Reddit帖子...');
            this.setStepActive('step1');
            
            const searchResponse = await this.apiCall('/api/search', 'POST', searchParams);
            
            if (!searchResponse.success) {
                throw new Error(searchResponse.error || '搜索失败');
            }
            
            // 第二阶段：获取Reddit数据
            this.updateProgress(50, '获取帖子内容和评论...');
            this.setStepActive('step2');
            this.setStepCompleted('step1');
            
            const redditResponse = await this.apiCall('/api/reddit/analyze', 'POST', {
                session_id: searchResponse.session_id,
                post_urls: searchResponse.post_urls
            });
            
            if (!redditResponse.success) {
                throw new Error(redditResponse.error || 'Reddit数据获取失败');
            }
            
            // 第三阶段：用户分析
            this.updateProgress(75, '分析用户画像...');
            this.setStepActive('step3');
            this.setStepCompleted('step2');
            
            // 第四阶段：生成报告
            this.updateProgress(90, '生成分析报告...');
            this.setStepActive('step4');
            this.setStepCompleted('step3');
            
            // 完成
            this.updateProgress(100, '分析完成！');
            this.setStepCompleted('step4');
            
            // 保存结果并显示
            this.currentResults = {
                search: searchResponse,
                reddit: redditResponse,
                query: query,
                timestamp: new Date().toISOString()
            };
            
            setTimeout(() => {
                this.showResults();
            }, 1000);
            
        } catch (error) {
            console.error('搜索失败:', error);
            this.showError(error.message || '搜索过程中出现未知错误');
        } finally {
            this.searchInProgress = false;
        }
    }
    
    async apiCall(endpoint, method = 'GET', data = null) {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };
        
        if (data) {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(endpoint, options);
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
    }
    
    async fetchSearchSuggestions(query) {
        try {
            const response = await this.apiCall(`/api/suggestions?q=${encodeURIComponent(query)}`);
            if (response.suggestions && response.suggestions.length > 0) {
                this.showSuggestions(response.suggestions);
            } else {
                this.hideSuggestions();
            }
        } catch (error) {
            console.warn('获取搜索建议失败:', error);
            this.hideSuggestions();
        }
    }
    
    showSuggestions(suggestions) {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        const suggestionsList = document.getElementById('suggestionsList');
        
        if (!suggestionsContainer || !suggestionsList) return;
        
        suggestionsList.innerHTML = '';
        
        suggestions.forEach(suggestion => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.textContent = suggestion;
            item.addEventListener('click', () => {
                document.getElementById('searchInput').value = suggestion;
                this.hideSuggestions();
                this.performSearch();
            });
            suggestionsList.appendChild(item);
        });
        
        suggestionsContainer.style.display = 'block';
    }
    
    hideSuggestions() {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        if (suggestionsContainer) {
            suggestionsContainer.style.display = 'none';
        }
    }
    
    feelingLucky() {
        const luckyQueries = [
            'python programming tips',
            'career advice tech',
            'startup founder stories',
            'machine learning beginner',
            'remote work productivity',
            'coding interview preparation',
            'web development trends',
            'data science projects'
        ];
        
        const randomQuery = luckyQueries[Math.floor(Math.random() * luckyQueries.length)];
        document.getElementById('searchInput').value = randomQuery;
        this.performSearch();
    }
    
    clearSearch() {
        document.getElementById('searchInput').value = '';
        document.getElementById('searchInput').focus();
        this.hideSuggestions();
    }
    
    newSearch() {
        this.clearSearch();
        this.showSearchSection();
        this.currentResults = null;
        this.currentSession = null;
    }
    
    showSearchSection() {
        this.hideAllSections();
        document.getElementById('searchSection').style.display = 'block';
    }
    
    showLoadingSection() {
        this.hideAllSections();
        document.getElementById('loadingSection').style.display = 'block';
        this.resetProgress();
    }
    
    showResults() {
        this.hideAllSections();
        document.getElementById('resultsSection').style.display = 'block';
        this.populateResults();
    }
    
    showError(message) {
        this.hideAllSections();
        document.getElementById('errorSection').style.display = 'block';
        document.getElementById('errorMessage').textContent = message;
    }
    
    hideAllSections() {
        const sections = ['searchSection', 'loadingSection', 'resultsSection', 'errorSection'];
        sections.forEach(sectionId => {
            const section = document.getElementById(sectionId);
            if (section) {
                section.style.display = 'none';
            }
        });
    }

    updateProgress(percentage, message) {
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');

        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }

        if (progressText) {
            progressText.textContent = `${percentage}%`;
        }

        console.log(`进度: ${percentage}% - ${message}`);
    }

    resetProgress() {
        this.updateProgress(0, '准备开始...');

        // 重置所有步骤状态
        const steps = ['step1', 'step2', 'step3', 'step4'];
        steps.forEach(stepId => {
            const step = document.getElementById(stepId);
            if (step) {
                step.classList.remove('active', 'completed');
            }
        });
    }

    setStepActive(stepId) {
        const step = document.getElementById(stepId);
        if (step) {
            step.classList.add('active');
            step.classList.remove('completed');
        }
    }

    setStepCompleted(stepId) {
        const step = document.getElementById(stepId);
        if (step) {
            step.classList.remove('active');
            step.classList.add('completed');
        }
    }

    switchTab(tabName) {
        // 更新标签按钮状态
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新标签内容显示
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        document.getElementById(`${tabName}Tab`).classList.add('active');
    }

    populateResults() {
        if (!this.currentResults) return;

        const { search, reddit, query } = this.currentResults;

        // 更新结果头部信息
        document.getElementById('queryDisplay').textContent = query;
        document.getElementById('resultsCount').textContent =
            `找到 ${search.post_urls?.length || 0} 个相关帖子`;
        document.getElementById('processingTime').textContent =
            `处理时间: ${reddit.statistics?.processing_time?.toFixed(2) || 0}秒`;

        // 更新统计卡片
        this.updateStatistics(reddit.statistics || {});

        // 填充各个标签页内容
        this.populatePostsTab(reddit.posts_data || []);
        this.populateCommentsTab(reddit.posts_data || []);
        this.populateUsersTab(reddit.user_histories || {});
        this.populateInsightsTab(reddit);
    }

    updateStatistics(stats) {
        document.getElementById('postsCount').textContent = stats.total_posts || 0;
        document.getElementById('commentsCount').textContent =
            this.calculateTotalComments(this.currentResults.reddit.posts_data || []);
        document.getElementById('usersCount').textContent = stats.total_commenters || 0;
        document.getElementById('subredditsCount').textContent =
            (stats.subreddits || []).length;
    }

    calculateTotalComments(postsData) {
        return postsData.reduce((total, postData) => {
            return total + (postData.comments?.length || 0);
        }, 0);
    }

    populatePostsTab(postsData) {
        const postsList = document.getElementById('postsList');
        if (!postsList) return;

        postsList.innerHTML = '';

        if (postsData.length === 0) {
            postsList.innerHTML = '<p class="no-results">未找到相关帖子</p>';
            return;
        }

        postsData.forEach((postData, index) => {
            const post = postData.post;
            if (!post) return;

            const postCard = document.createElement('div');
            postCard.className = 'post-card fade-in';
            postCard.style.animationDelay = `${index * 0.1}s`;

            postCard.innerHTML = `
                <div class="post-header">
                    <div>
                        <h3 class="post-title">${this.escapeHtml(post.title)}</h3>
                        <div class="post-meta">
                            <span class="post-subreddit">r/${post.subreddit}</span>
                            <span class="post-score">
                                <i class="fas fa-arrow-up"></i>
                                ${post.score}
                            </span>
                            <span class="post-comments">
                                <i class="fas fa-comments"></i>
                                ${post.num_comments}
                            </span>
                            <span class="post-time">
                                ${this.formatDate(post.created_datetime)}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="post-content">
                    ${this.escapeHtml(post.selftext?.substring(0, 300) || '')}
                    ${post.selftext?.length > 300 ? '...' : ''}
                </div>
                <div class="post-actions">
                    <a href="${post.reddit_url}" target="_blank" class="post-link">
                        <i class="fab fa-reddit"></i>
                        在Reddit中查看
                    </a>
                    <span class="post-engagement">
                        参与度: ${post.engagement_score?.toFixed(1) || 0}
                    </span>
                </div>
            `;

            postsList.appendChild(postCard);
        });
    }

    populateCommentsTab(postsData) {
        const commentsList = document.getElementById('commentsList');
        if (!commentsList) return;

        commentsList.innerHTML = '';

        // 收集所有评论
        const allComments = [];
        postsData.forEach(postData => {
            if (postData.comments) {
                postData.comments.forEach(comment => {
                    allComments.push({
                        ...comment,
                        post_title: postData.post?.title || '未知帖子'
                    });
                });
            }
        });

        // 按分数排序
        allComments.sort((a, b) => b.score - a.score);

        if (allComments.length === 0) {
            commentsList.innerHTML = '<p class="no-results">未找到评论</p>';
            return;
        }

        allComments.slice(0, 20).forEach((comment, index) => {
            const commentCard = document.createElement('div');
            commentCard.className = 'comment-card slide-in';
            commentCard.style.animationDelay = `${index * 0.05}s`;

            commentCard.innerHTML = `
                <div class="comment-header">
                    <span class="comment-author">u/${comment.author}</span>
                    <span class="comment-score">
                        <i class="fas fa-arrow-up"></i>
                        ${comment.score}
                    </span>
                </div>
                <div class="comment-body">
                    ${this.escapeHtml(comment.body?.substring(0, 500) || '')}
                    ${comment.body?.length > 500 ? '...' : ''}
                </div>
                <div class="comment-meta">
                    <small>来自: ${this.escapeHtml(comment.post_title)}</small>
                </div>
            `;

            commentsList.appendChild(commentCard);
        });
    }

    populateUsersTab(userHistories) {
        const usersList = document.getElementById('usersList');
        if (!usersList) return;

        usersList.innerHTML = '';

        const users = Object.keys(userHistories);

        if (users.length === 0) {
            usersList.innerHTML = '<p class="no-results">未找到用户数据</p>';
            return;
        }

        users.slice(0, 12).forEach((username, index) => {
            const userHistory = userHistories[username];
            if (!userHistory || Object.keys(userHistory).length === 0) return;

            // 获取第一个子版块的数据作为示例
            const firstSubreddit = Object.keys(userHistory)[0];
            const userData = userHistory[firstSubreddit];

            if (!userData || !userData.user) return;

            const user = userData.user;
            const userCard = document.createElement('div');
            userCard.className = 'user-card fade-in';
            userCard.style.animationDelay = `${index * 0.1}s`;

            userCard.innerHTML = `
                <div class="user-header">
                    <div class="user-avatar">
                        ${username.charAt(0).toUpperCase()}
                    </div>
                    <div class="user-info">
                        <div class="user-name">u/${username}</div>
                        <div class="user-karma">
                            总Karma: ${user.total_karma?.toLocaleString() || 0}
                        </div>
                    </div>
                </div>
                <div class="user-stats">
                    <div class="user-stat">
                        <div class="user-stat-number">${userData.statistics?.total_posts || 0}</div>
                        <div class="user-stat-label">帖子</div>
                    </div>
                    <div class="user-stat">
                        <div class="user-stat-number">${userData.statistics?.total_comments || 0}</div>
                        <div class="user-stat-label">评论</div>
                    </div>
                    <div class="user-stat">
                        <div class="user-stat-number">${userData.statistics?.average_post_score?.toFixed(1) || 0}</div>
                        <div class="user-stat-label">平均帖子分数</div>
                    </div>
                    <div class="user-stat">
                        <div class="user-stat-number">${user.account_age_days || 0}</div>
                        <div class="user-stat-label">账户天数</div>
                    </div>
                </div>
            `;

            usersList.appendChild(userCard);
        });
    }

    populateInsightsTab(redditData) {
        const insightsContent = document.getElementById('insightsContent');
        if (!insightsContent) return;

        insightsContent.innerHTML = '';

        const insights = this.generateInsights(redditData);

        insights.forEach((insight, index) => {
            const insightCard = document.createElement('div');
            insightCard.className = 'insight-card fade-in';
            insightCard.style.animationDelay = `${index * 0.2}s`;

            insightCard.innerHTML = `
                <h3 class="insight-title">
                    <i class="${insight.icon}"></i>
                    ${insight.title}
                </h3>
                <div class="insight-content">
                    ${insight.content}
                </div>
            `;

            insightsContent.appendChild(insightCard);
        });
    }

    generateInsights(redditData) {
        const insights = [];
        const stats = redditData.statistics || {};
        const postsData = redditData.posts_data || [];

        // 热门话题洞察
        if (stats.subreddits && stats.subreddits.length > 0) {
            insights.push({
                icon: 'fas fa-fire',
                title: '热门讨论社区',
                content: `本次搜索主要涉及 ${stats.subreddits.length} 个子版块，其中最活跃的是 r/${stats.subreddits[0]}。这些社区反映了用户对相关话题的高度关注。`
            });
        }

        // 用户参与度洞察
        if (stats.total_commenters > 0) {
            insights.push({
                icon: 'fas fa-users',
                title: '用户参与分析',
                content: `共发现 ${stats.total_commenters} 位活跃用户参与讨论。这些用户的历史数据显示了他们在相关领域的专业程度和参与热情。`
            });
        }

        // 内容质量洞察
        const avgScore = this.calculateAverageScore(postsData);
        if (avgScore > 0) {
            insights.push({
                icon: 'fas fa-chart-line',
                title: '内容质量评估',
                content: `分析的帖子平均得分为 ${avgScore.toFixed(1)}，表明内容质量${avgScore > 50 ? '较高' : '中等'}。高质量的讨论往往能够吸引更多用户参与和互动。`
            });
        }

        // 时间趋势洞察
        insights.push({
            icon: 'fas fa-clock',
            title: '讨论时间分布',
            content: `通过分析发帖和评论的时间分布，可以发现用户在特定时间段更活跃，这有助于理解社区的活动模式。`
        });

        return insights;
    }

    calculateAverageScore(postsData) {
        if (postsData.length === 0) return 0;

        const totalScore = postsData.reduce((sum, postData) => {
            return sum + (postData.post?.score || 0);
        }, 0);

        return totalScore / postsData.length;
    }

    async exportResults() {
        if (!this.currentResults) {
            this.showError('没有可导出的结果');
            return;
        }

        try {
            const exportData = {
                query: this.currentResults.query,
                timestamp: this.currentResults.timestamp,
                results: this.currentResults,
                export_time: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });

            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cogbridges_search_${this.currentResults.query.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

        } catch (error) {
            console.error('导出失败:', error);
            this.showError('导出数据时出现错误');
        }
    }

    async loadRecentSessions() {
        try {
            const response = await this.apiCall('/api/sessions');
            if (response.sessions && response.sessions.length > 0) {
                // 可以在这里显示最近的搜索历史
                console.log('最近的搜索会话:', response.sessions);
            }
        } catch (error) {
            console.warn('加载搜索历史失败:', error);
        }
    }

    // 工具方法
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatDate(dateString) {
        if (!dateString) return '未知时间';

        try {
            const date = new Date(dateString);
            const now = new Date();
            const diffMs = now - date;
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

            if (diffDays === 0) {
                return '今天';
            } else if (diffDays === 1) {
                return '昨天';
            } else if (diffDays < 7) {
                return `${diffDays}天前`;
            } else if (diffDays < 30) {
                return `${Math.floor(diffDays / 7)}周前`;
            } else if (diffDays < 365) {
                return `${Math.floor(diffDays / 30)}个月前`;
            } else {
                return `${Math.floor(diffDays / 365)}年前`;
            }
        } catch (error) {
            return '未知时间';
        }
    }

    // 错误处理
    handleError(error, context = '') {
        console.error(`错误 ${context}:`, error);

        let errorMessage = '出现了未知错误';

        if (error.message) {
            errorMessage = error.message;
        } else if (typeof error === 'string') {
            errorMessage = error;
        }

        this.showError(errorMessage);
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.cogBridgesApp = new CogBridgesApp();

    // 添加一些全局事件监听
    window.addEventListener('beforeunload', (e) => {
        if (window.cogBridgesApp.searchInProgress) {
            e.preventDefault();
            e.returnValue = '搜索正在进行中，确定要离开吗？';
        }
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K 聚焦搜索框
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        // Escape 键返回搜索页面
        if (e.key === 'Escape' && !window.cogBridgesApp.searchInProgress) {
            window.cogBridgesApp.showSearchSection();
        }
    });

    console.log('🚀 CogBridges Search 应用已初始化');
});
