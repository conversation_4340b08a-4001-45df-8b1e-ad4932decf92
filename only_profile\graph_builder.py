"""
人格图谱构建模块
复用resona的graph_builder和user_profiler，将Reddit用户数据和语义分析结果转换为人格图谱
"""
import asyncio
import logging
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime
import sys
import os
import json  # 添加缺失的json导入

# 导入本地模块
from replicate_server import ReplicateAIService
from llm_prompt_manager import get_prompt_manager, PromptType
from resona.services.vector_service import VectorService
from local_config import config as local_config

# 添加resona路径（用于数据模型）
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'resona'))

# 强制使用本地模型定义以避免枚举冲突
# try:
#     from resona.models import (
#         RedditUser, ParsedQuery, UserProfile, UserGraph,
#         NodeType, RelationType, GraphNode, GraphEdge
#     )
# except ImportError:
# 创建本地模型定义
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum
import uuid

class RedditPost(BaseModel):
    id: str = ""
    text: str
    timestamp: datetime = Field(default_factory=datetime.now)
    subreddit: str = ""
    score: int = 0

class RedditComment(BaseModel):
    id: str = ""
    text: str
    timestamp: datetime = Field(default_factory=datetime.now)
    subreddit: str = ""
    score: int = 0

class RedditUser(BaseModel):
    username: str
    posts: List[RedditPost] = Field(default_factory=list)
    comments: List[RedditComment] = Field(default_factory=list)

class ParsedQuery(BaseModel):
        original_text: str = Field(..., description="原始输入文本")
        search_intent: List[str] = Field(default_factory=list, description="Reddit搜索关键词")
        values_info: Dict[str, Any] = Field(default_factory=dict, description="初始价值观信息")
        emotional_state: Dict[str, float] = Field(default_factory=dict, description="情绪状态评分")
        topics: List[str] = Field(default_factory=list, description="主要话题")
        confidence: float = Field(default=1.0, description="解析置信度")
        core_concerns: List[str] = Field(default_factory=list, description="核心关切点")
        decision_points: List[str] = Field(default_factory=list, description="面临的决策点")
        life_domains: List[str] = Field(default_factory=list, description="涉及的生活领域")
        support_needs: List[str] = Field(default_factory=list, description="需要的支持类型")

class NodeType(str, Enum):
    BELIEF = "belief"
    VALUE = "value"
    EMOTION = "emotion"
    EXPERIENCE = "experience"
    GOAL = "goal"
    RELATIONSHIP = "relationship"
    SKILL = "skill"
    INTEREST = "interest"
    CONCERN = "concern"
    DECISION = "decision"

class RelationType(str, Enum):
    INFLUENCES = "influences"
    CONFLICTS_WITH = "conflicts_with"
    SUPPORTS = "supports"
    LEADS_TO = "leads_to"
    CAUSED_BY = "caused_by"
    RELATED_TO = "related_to"
    DEPENDS_ON = "depends_on"
    REINFORCES = "reinforces"

class GraphNode(BaseModel):
    node_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    node_type: NodeType
    content: str
    weight: float = Field(default=1.0, ge=0.0, le=1.0)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.now)

class GraphEdge(BaseModel):
    edge_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    source_node_id: str
    target_node_id: str
    relation_type: RelationType
    weight: float = Field(default=1.0, ge=0.0, le=1.0)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.now)

class UserGraph(BaseModel):
    nodes: Dict[str, GraphNode] = Field(default_factory=dict)  # 使用字典存储节点
    edges: List[GraphEdge] = Field(default_factory=list)
    user_id: str = ""

    def add_node(self, node: GraphNode):
        self.nodes[node.node_id] = node

    def add_edge(self, edge: GraphEdge):
        self.edges.append(edge)

    def get_nodes_by_type(self, node_type: NodeType) -> List[GraphNode]:
        return [node for node in self.nodes.values() if node.node_type == node_type]

    def get_node_count_by_type(self) -> Dict[str, int]:
        """获取各类型节点的数量统计"""
        counts = {}
        for node in self.nodes.values():
            node_type_str = node.node_type.value
            counts[node_type_str] = counts.get(node_type_str, 0) + 1
        return counts

class UserProfile(BaseModel):
    user_id: str
    graph: UserGraph = Field(default_factory=UserGraph)
    completeness_score: float = Field(default=0.0, ge=0.0, le=1.0)
    missing_dimensions: List[str] = Field(default_factory=list)
    version: str = Field(default="1.0")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    metadata: Dict[str, Any] = Field(default_factory=dict)

    def update_completeness(self) -> float:
        """计算并更新图谱完整性分数"""
        if not self.graph or not self.graph.nodes:
            self.completeness_score = 0.0
            self.missing_dimensions = list(NodeType.__members__.keys())
            return self.completeness_score

        # 计算节点类型覆盖度
        node_type_counts = self.graph.get_node_count_by_type()
        total_node_types = len(NodeType.__members__)
        covered_types = len(node_type_counts)
        type_coverage = covered_types / total_node_types

        # 计算节点数量质量（更多节点 = 更完整）
        total_nodes = len(self.graph.nodes)
        node_quality = min(total_nodes / 10.0, 1.0)  # 10个节点为满分

        # 计算边连接质量（更多边 = 更好的关系建模）
        total_edges = len(self.graph.edges)
        edge_quality = min(total_edges / max(total_nodes - 1, 1), 1.0)

        # 综合计算完整性分数
        self.completeness_score = (type_coverage * 0.4 + node_quality * 0.4 + edge_quality * 0.2)

        # 识别缺失的维度
        all_types = set(NodeType.__members__.keys())
        covered_types_set = set(node_type_counts.keys())
        self.missing_dimensions = list(all_types - covered_types_set)

        # 更新时间戳
        self.updated_at = datetime.now()

        return self.completeness_score

logger = logging.getLogger(__name__)

class PersonalityGraphBuilder:
    """人格图谱构建器 - 将Reddit用户数据转换为人格图谱"""
    
    def __init__(self):
        """初始化人格图谱构建器"""
        self.ai_service = ReplicateAIService()
        # 兼容不同配置来源（local_config 可能不存在这些属性）
        self.db_path: str = getattr(local_config, "GRAPH_STORAGE_DB_PATH", "data/graph_storage.db")

        # 初始化向量服务（内部基于 resona.config.settings 配置索引路径）
        self.vector_service = VectorService()
        # 记录索引路径供日志或后续使用
        self.faiss_path = getattr(local_config, "FAISS_INDEX_PATH", "./data/faiss_index")
        self.prompt_manager = get_prompt_manager()

        logger.info("PersonalityGraphBuilder 初始化完成")
        logger.info(f"  - 数据库路径: {self.db_path}")
        
        # 图谱构建配置
        self.max_content_per_analysis = 5000  # 单次分析最大内容长度
        self.min_graph_nodes = 5  # 最小图节点数
        self.max_graph_complexity = 50  # 最大图复杂度
    
    async def build_personality_graph(self, reddit_user: RedditUser, 
                                    semantic_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        构建人格图谱
        
        Args:
            reddit_user: Reddit用户数据
            semantic_analysis: 语义分析结果
            
        Returns:
            Dict: 图谱构建结果
        """
        logger.info(f"开始构建人格图谱: {reddit_user.username}")
        
        try:
            # 1. 准备内容数据
            content_data = self._prepare_content_for_graph(reddit_user)
            
            if not content_data:
                return {
                    "success": False,
                    "error": "无足够内容构建图谱",
                    "username": reddit_user.username
                }
            
            # 2. 使用AI构建用户图谱
            logger.info("使用AI构建用户图谱...")
            user_graph = await self._build_user_graph_with_ai(
                content_data["texts"],
                {"username": reddit_user.username, "context": content_data["context"]},
                semantic_analysis
            )

            # 3. 构建完整用户画像
            logger.info("构建完整用户画像...")
            user_profile = await self._build_user_profile(
                reddit_user, user_graph, semantic_analysis
            )
            
            # 5. 生成图谱解释
            logger.info("生成图谱解释...")
            graph_explanation = await self._generate_graph_explanation(
                user_profile, semantic_analysis
            )
            
            # 6. 计算图谱质量指标
            quality_metrics = self._calculate_graph_quality(user_profile.graph)
            
            logger.info(f"人格图谱构建完成: {reddit_user.username}")
            
            return {
                "success": True,
                "username": reddit_user.username,
                "user_profile": user_profile,
                "graph_explanation": graph_explanation,
                "quality_metrics": quality_metrics,
                "content_stats": content_data["stats"],
                "build_timestamp": datetime.now()
            }
            
        except Exception as e:
            logger.error(f"构建人格图谱时发生错误: {e}")
            return {
                "success": False,
                "error": f"图谱构建失败: {str(e)}",
                "username": reddit_user.username
            }
    
    def _prepare_content_for_graph(self, reddit_user: RedditUser) -> Optional[Dict[str, Any]]:
        """
        准备内容数据用于图谱构建
        
        Args:
            reddit_user: Reddit用户数据
            
        Returns:
            Dict: 处理后的内容数据
        """
        try:
            # 收集和处理内容
            all_texts = []
            content_metadata = []
            
            # 处理帖子
            for post in reddit_user.posts:
                if len(post.text) > 100:  # 过滤过短的内容
                    text = f"【{post.timestamp.strftime('%Y年%m月')}】在r/{post.subreddit}发帖：{post.text}"
                    all_texts.append(text)
                    content_metadata.append({
                        "type": "post",
                        "subreddit": post.subreddit,
                        "timestamp": post.timestamp,
                        "score": post.score,
                        "length": len(post.text)
                    })
            
            # 处理评论
            for comment in reddit_user.comments:
                if len(comment.text) > 50:  # 过滤过短的评论
                    text = f"【{comment.timestamp.strftime('%Y年%m月')}】在r/{comment.subreddit}评论：{comment.text}"
                    all_texts.append(text)
                    content_metadata.append({
                        "type": "comment",
                        "subreddit": comment.subreddit,
                        "timestamp": comment.timestamp,
                        "score": comment.score,
                        "length": len(comment.text)
                    })
            
            if not all_texts:
                return None
            
            # 按时间排序（保持时间线信息）
            combined_data = list(zip(all_texts, content_metadata))
            combined_data.sort(key=lambda x: x[1]["timestamp"])
            
            all_texts = [item[0] for item in combined_data]
            content_metadata = [item[1] for item in combined_data]
            
            # 生成上下文信息
            context = f"用户{reddit_user.username}的Reddit发言历史，包含{len(reddit_user.posts)}个帖子和{len(reddit_user.comments)}个评论"
            
            # 如果内容过多，进行智能采样
            if len(all_texts) > 20:
                # 优先保留评分高的内容和时间分布均匀的内容
                sampled_texts = self._smart_sample_content(all_texts, content_metadata, 20)
                all_texts = sampled_texts
            
            # 计算统计信息
            stats = {
                "total_posts": len(reddit_user.posts),
                "total_comments": len(reddit_user.comments),
                "processed_texts": len(all_texts),
                "avg_content_length": sum(len(text) for text in all_texts) / len(all_texts),
                "subreddit_count": len(set(meta["subreddit"] for meta in content_metadata)),
                "date_range": {
                    "start": min(meta["timestamp"] for meta in content_metadata),
                    "end": max(meta["timestamp"] for meta in content_metadata)
                }
            }
            
            return {
                "texts": all_texts,
                "context": context,
                "metadata": content_metadata,
                "stats": stats
            }
            
        except Exception as e:
            logger.error(f"准备内容数据时发生错误: {e}")
            return None
    
    def _smart_sample_content(self, texts: List[str], metadata: List[Dict], target_count: int) -> List[str]:
        """
        智能采样内容
        
        Args:
            texts: 文本列表
            metadata: 元数据列表
            target_count: 目标数量
            
        Returns:
            List[str]: 采样后的文本
        """
        if len(texts) <= target_count:
            return texts
        
        # 按多个维度评分
        scored_items = []
        for i, (text, meta) in enumerate(zip(texts, metadata)):
            score = 0
            
            # 评分因子
            score += meta["score"] * 0.3  # Reddit评分
            score += min(meta["length"] / 200, 2) * 0.2  # 内容长度（适中加分）
            score += (1 if meta["type"] == "post" else 0.7) * 0.2  # 帖子比评论重要
            
            # 时间分布加分（保持时间线的完整性）
            time_position = i / len(texts)
            time_diversity_bonus = 1 - abs(time_position - 0.5)  # 中间时间段加分
            score += time_diversity_bonus * 0.3
            
            scored_items.append((score, i, text))
        
        # 按评分排序并取前N个
        scored_items.sort(key=lambda x: x[0], reverse=True)
        selected_texts = [item[2] for item in scored_items[:target_count]]
        
        return selected_texts

    async def _build_user_graph_with_ai(self, texts: List[str], context: Dict[str, Any],
                                       semantic_analysis: Dict[str, Any]) -> UserGraph:
        """
        使用AI构建用户图谱

        Args:
            texts: 用户文本内容
            context: 用户上下文信息
            semantic_analysis: 语义分析结果

        Returns:
            UserGraph: 构建的用户图谱
        """
        try:
            # 准备内容摘要
            content_summary = "\n".join(texts[:10])  # 取前10条内容

            # 统计帖子/评论数量
            posts_included = sum(1 for t in texts if "发帖" in t)
            comments_included = sum(1 for t in texts if "评论" in t)
            logger.debug("图谱构建 - 拼接内容统计: %d 帖子, %d 评论, 总条目 %d", posts_included, comments_included, len(texts))

            # 使用 Prompt Manager 生成 Prompt
            prompt_config = self.prompt_manager.get_full_prompt_config(
                PromptType.PERSONALITY_GRAPH_BUILDING,
                username=context.get('username', 'unknown'),
                content_count=len(texts),
                content_summary=content_summary,
                topics=self._safe_get_parsed_query_attr(semantic_analysis, 'topics', []),
                emotional_state=self._safe_get_parsed_query_attr(semantic_analysis, 'emotional_state', {})
            )

            response = await self.ai_service.get_completion(
                prompt=prompt_config['prompt'],
                max_tokens=prompt_config['max_tokens'],
                temperature=prompt_config['temperature'],
                system_prompt=prompt_config['system_prompt']
            )

            # 解析AI响应
            graph_data = json.loads(self.ai_service._extract_json_from_response(response))

            # 构建UserGraph对象
            user_graph = UserGraph(user_id=context.get('username', 'unknown'))

            # 添加节点
            nodes_added = 0
            for node_data in graph_data.get("nodes", []):
                try:
                    node = GraphNode(
                        node_id=node_data["node_id"],
                        node_type=self._parse_node_type(node_data["node_type"]),
                        content=node_data["content"],
                        weight=node_data.get("weight", 0.5),
                        metadata=node_data.get("metadata", {})
                    )
                    user_graph.add_node(node)
                    nodes_added += 1
                    logger.debug(f"添加AI生成节点: {node.node_id} ({node.node_type})")
                except Exception as e:
                    logger.error(f"添加节点失败: {e}, 节点数据: {node_data}")

            # 添加边
            edges_added = 0
            for edge_data in graph_data.get("edges", []):
                try:
                    edge = GraphEdge(
                        source_node_id=edge_data["source_id"],
                        target_node_id=edge_data["target_id"],
                        relation_type=self._parse_relation_type(edge_data["relation_type"]),
                        weight=edge_data.get("weight", 0.5),
                        metadata={"evidence": edge_data.get("evidence", "")}
                    )
                    user_graph.add_edge(edge)
                    edges_added += 1
                    logger.debug(f"添加AI生成边: {edge.source_node_id} -> {edge.target_node_id} ({edge.relation_type})")
                except Exception as e:
                    logger.error(f"添加边失败: {e}, 边数据: {edge_data}")

            logger.info(f"AI图谱构建完成: 添加了 {nodes_added} 个节点, {edges_added} 条边")
            # DEBUG: 输出节点类型统计及部分节点示例
            try:
                type_counts = user_graph.get_node_count_by_type()
                logger.debug("节点类型统计: %s", type_counts)
                sample_nodes = [{"id": n.node_id, "type": n.node_type.value} for n in list(user_graph.nodes.values())[:5]]
                logger.debug("节点示例(前5): %s", sample_nodes)
            except Exception as e:
                logger.warning("统计节点信息时发生错误: %s", e)
            return user_graph

        except Exception as e:
            logger.error(f"AI构建用户图谱失败: {e}", exc_info=True)
            # 返回基础图谱
            return self._create_basic_graph(context.get('username', 'unknown'))

    def _safe_get_parsed_query_attr(self, semantic_analysis: Dict[str, Any], attr_name: str, default_value):
        """安全获取ParsedQuery对象的属性"""
        try:
            parsed_query = semantic_analysis.get('parsed_query', {})
            if hasattr(parsed_query, attr_name):
                return getattr(parsed_query, attr_name)
            elif isinstance(parsed_query, dict):
                return parsed_query.get(attr_name, default_value)
            else:
                return default_value
        except Exception:
            return default_value

    def _parse_node_type(self, type_str: str) -> NodeType:
        """解析节点类型"""
        type_mapping = {
            "PERSONALITY": NodeType.SKILL,  # 映射到技能类型
            "INTEREST": NodeType.INTEREST,
            "BELIEF": NodeType.BELIEF,
            "EMOTION": NodeType.EMOTION,
            "BEHAVIOR": NodeType.EXPERIENCE,  # 映射到经验类型
            "TOPIC": NodeType.CONCERN,  # 映射到关注点类型
            "VALUE": NodeType.VALUE,
            "PREFERENCE": NodeType.GOAL,  # 映射到目标类型
            "SKILL": NodeType.SKILL,
            "EXPERIENCE": NodeType.EXPERIENCE,
            "GOAL": NodeType.GOAL,
            "RELATIONSHIP": NodeType.RELATIONSHIP,
            "CONCERN": NodeType.CONCERN,
            "DECISION": NodeType.DECISION
        }
        return type_mapping.get(type_str.upper(), NodeType.INTEREST)

    def _parse_relation_type(self, type_str: str) -> RelationType:
        """解析关系类型，处理AI生成的关系类型与枚举值的映射"""
        type_mapping = {
            # 直接匹配的关系类型
            "INFLUENCES": RelationType.INFLUENCES,
            "SUPPORTS": RelationType.SUPPORTS,
            "LEADS_TO": RelationType.LEADS_TO,
            "CAUSED_BY": RelationType.CAUSED_BY,
            "RELATED_TO": RelationType.RELATED_TO,
            "DEPENDS_ON": RelationType.DEPENDS_ON,
            "REINFORCES": RelationType.REINFORCES,
            "CONFLICTS_WITH": RelationType.CONFLICTS_WITH,

            # AI可能生成的变体映射到实际枚举值
            "CONFLICTS": RelationType.CONFLICTS_WITH,  # 映射CONFLICTS到CONFLICTS_WITH
            "CAUSES": RelationType.LEADS_TO,  # 映射CAUSES到LEADS_TO
            "RELATES": RelationType.RELATED_TO,  # 映射RELATES到RELATED_TO
            "LEADS": RelationType.LEADS_TO,  # 映射LEADS到LEADS_TO
            "DEPENDS": RelationType.DEPENDS_ON,  # 映射DEPENDS到DEPENDS_ON
            "REINFORCED_BY": RelationType.REINFORCES,  # 映射REINFORCED_BY到REINFORCES
            "INFLUENCED_BY": RelationType.INFLUENCES,  # 映射INFLUENCED_BY到INFLUENCES
            "SUPPORTED_BY": RelationType.SUPPORTS,  # 映射SUPPORTED_BY到SUPPORTS
        }

        parsed_type = type_mapping.get(type_str.upper(), RelationType.RELATED_TO)
        logger.debug(f"解析关系类型: {type_str} -> {parsed_type}")
        return parsed_type

    def _create_basic_graph(self, user_id: str) -> UserGraph:
        """创建基础图谱（当AI构建失败时使用）"""
        graph = UserGraph(user_id=user_id)

        # 添加基础节点
        basic_node = GraphNode(
            node_id=f"basic_{user_id}",
            node_type=NodeType.SKILL,  # 使用存在的枚举值
            content="基础人格特征",
            weight=0.5,
            metadata={"source": "fallback"}
        )
        graph.add_node(basic_node)

        return graph
    
    async def _enhance_graph_with_semantic_analysis(self, user_graph: UserGraph,
                                                  semantic_analysis: Dict[str, Any]) -> UserGraph:
        """
        基于语义分析结果增强图谱

        Args:
            user_graph: 基础用户图谱
            semantic_analysis: 语义分析结果

        Returns:
            UserGraph: 增强后的图谱
        """
        try:
            enhanced_graph = user_graph
            initial_node_count = len(enhanced_graph.nodes)
            logger.info(f"开始图谱增强，初始节点数: {initial_node_count}")

            # 记录语义分析结果的结构
            logger.debug(f"语义分析结果键: {list(semantic_analysis.keys())}")

            # 1. 添加从语义分析中提取的节点
            if semantic_analysis.get("success") and semantic_analysis.get("parsed_query"):
                parsed_query = semantic_analysis["parsed_query"]
                logger.info(f"找到parsed_query，类型: {type(parsed_query)}")

                # 记录parsed_query的属性
                if hasattr(parsed_query, '__dict__'):
                    logger.debug(f"parsed_query属性: {list(parsed_query.__dict__.keys())}")
                elif hasattr(parsed_query, 'dict'):
                    logger.debug(f"parsed_query字典键: {list(parsed_query.dict().keys())}")
                else:
                    logger.debug(f"parsed_query内容: {parsed_query}")
                
                # 添加情绪节点
                emotion_count = 0
                if hasattr(parsed_query, 'emotional_state') and parsed_query.emotional_state:
                    logger.info(f"处理情绪状态: {parsed_query.emotional_state}")
                    for emotion, intensity in parsed_query.emotional_state.items():
                        logger.debug(f"处理情绪: {emotion}, 强度: {intensity}")
                        if intensity > 0.4:  # 显著情绪
                            emotion_node = GraphNode(
                                node_id=f"emotion_{emotion}_{user_graph.user_id}",
                                node_type=NodeType.EMOTION,
                                content=f"{emotion}情绪（强度：{intensity:.2f}）",
                                weight=intensity,
                                metadata={
                                    "source": "semantic_analysis",
                                    "intensity": intensity,
                                    "emotion_type": emotion
                                }
                            )
                            enhanced_graph.add_node(emotion_node)
                            emotion_count += 1
                            logger.debug(f"添加情绪节点: {emotion_node.node_id}")
                else:
                    logger.warning("parsed_query中没有emotional_state或为空")

                logger.info(f"添加了 {emotion_count} 个情绪节点")
                
                # 添加话题节点
                topic_count = 0
                if hasattr(parsed_query, 'topics') and parsed_query.topics:
                    logger.info(f"处理话题: {parsed_query.topics}")
                    for topic in parsed_query.topics:
                        if topic and topic.strip():  # 确保话题不为空
                            topic_node = GraphNode(
                                node_id=f"topic_{topic.replace(' ', '_')}_{user_graph.user_id}",
                                node_type=NodeType.CONCERN,  # 使用CONCERN而不是TOPIC
                                content=f"关注话题：{topic}",
                                weight=0.7,
                                metadata={
                                    "source": "semantic_analysis",
                                    "topic": topic
                                }
                            )
                            enhanced_graph.add_node(topic_node)
                            topic_count += 1
                            logger.debug(f"添加话题节点: {topic_node.node_id}")
                else:
                    logger.warning("parsed_query中没有topics或为空")

                logger.info(f"添加了 {topic_count} 个话题节点")
                
                # 添加价值观节点
                belief_count = 0
                if hasattr(parsed_query, 'values_info') and parsed_query.values_info:
                    logger.info(f"处理价值观信息: {parsed_query.values_info}")
                    for key, value in parsed_query.values_info.items():
                        if value and str(value).lower() not in ["unknown", "未知", "", "none"]:
                            belief_node = GraphNode(
                                node_id=f"belief_{key.replace(' ', '_')}_{user_graph.user_id}",
                                node_type=NodeType.BELIEF,
                                content=f"{key}：{value}",
                                weight=0.8,
                                metadata={
                                    "source": "semantic_analysis",
                                    "value_dimension": key,
                                    "value": value
                                }
                            )
                            enhanced_graph.add_node(belief_node)
                            belief_count += 1
                            logger.debug(f"添加价值观节点: {belief_node.node_id}")
                else:
                    logger.warning("parsed_query中没有values_info或为空")

                logger.info(f"添加了 {belief_count} 个价值观节点")
            
            # 2. 添加从用户特征分析中提取的节点
            personality_count = 0
            interest_count = 0

            if semantic_analysis.get("user_characteristics"):
                char_data = semantic_analysis["user_characteristics"]
                logger.info(f"处理用户特征数据: {list(char_data.keys())}")

                # 添加性格特征节点
                personality = char_data.get("personality_traits", {})
                if personality:
                    logger.info(f"处理性格特征: {personality}")
                    for trait, score in personality.items():
                        if isinstance(score, (int, float)) and score > 0.6:  # 显著特征
                            trait_node = GraphNode(
                                node_id=f"personality_{trait.replace(' ', '_')}_{user_graph.user_id}",
                                node_type=NodeType.BELIEF,
                                content=f"性格特征：{trait}（{score:.2f}）",
                                weight=score,
                                metadata={
                                    "source": "personality_analysis",
                                    "trait": trait,
                                    "score": score
                                }
                            )
                            enhanced_graph.add_node(trait_node)
                            personality_count += 1
                            logger.debug(f"添加性格特征节点: {trait_node.node_id}")
                else:
                    logger.warning("用户特征中没有personality_traits")

                logger.info(f"添加了 {personality_count} 个性格特征节点")
                
                # 添加兴趣节点
                interests = char_data.get("interests_and_values", {}).get("main_interests", [])
                if interests:
                    logger.info(f"处理兴趣: {interests}")
                    for interest in interests:
                        if interest and interest != "未知" and interest.strip():
                            interest_node = GraphNode(
                                node_id=f"interest_{interest.replace(' ', '_')}_{user_graph.user_id}",
                                node_type=NodeType.INTEREST,  # 使用INTEREST而不是TOPIC
                                content=f"兴趣：{interest}",
                                weight=0.6,
                                metadata={
                                    "source": "interest_analysis",
                                    "interest": interest
                                }
                            )
                            enhanced_graph.add_node(interest_node)
                            interest_count += 1
                            logger.debug(f"添加兴趣节点: {interest_node.node_id}")
                else:
                    logger.warning("用户特征中没有main_interests")

                logger.info(f"添加了 {interest_count} 个兴趣节点")
            else:
                logger.warning("语义分析结果中没有user_characteristics")
            
            # 3. 增强节点间的连接
            enhanced_graph = self._enhance_node_connections(enhanced_graph)

            final_node_count = len(enhanced_graph.nodes)
            nodes_added = final_node_count - initial_node_count

            logger.info(f"图谱增强完成！")
            logger.info(f"  - 初始节点数: {initial_node_count}")
            logger.info(f"  - 最终节点数: {final_node_count}")
            logger.info(f"  - 新增节点数: {nodes_added}")
            logger.info(f"  - 边数: {len(enhanced_graph.edges)}")

            # 记录节点类型分布
            node_types = {}
            for node in enhanced_graph.nodes.values():
                node_type = node.node_type.value
                node_types[node_type] = node_types.get(node_type, 0) + 1
            logger.info(f"  - 节点类型分布: {node_types}")

            return enhanced_graph

        except Exception as e:
            logger.error(f"图谱增强时发生错误: {e}", exc_info=True)
            return user_graph
    
    def _enhance_node_connections(self, graph: UserGraph) -> UserGraph:
        """
        增强节点间的连接
        
        Args:
            graph: 用户图谱
            
        Returns:
            UserGraph: 增强连接后的图谱
        """
        try:
            # 获取不同类型的节点
            emotion_nodes = [node for node in graph.nodes.values() if node.node_type == NodeType.EMOTION]
            belief_nodes = [node for node in graph.nodes.values() if node.node_type == NodeType.BELIEF]
            topic_nodes = [node for node in graph.nodes.values() if node.node_type == NodeType.CONCERN]  # 使用CONCERN
            experience_nodes = [node for node in graph.nodes.values() if node.node_type == NodeType.EXPERIENCE]
            
            # 添加情绪与信念的连接
            for emotion_node in emotion_nodes:
                for belief_node in belief_nodes:
                    if self._should_connect_emotion_belief(emotion_node, belief_node):
                        edge = GraphEdge(
                            source_node_id=emotion_node.node_id,
                            target_node_id=belief_node.node_id,
                            relation_type=RelationType.INFLUENCES,
                            weight=0.6,
                            metadata={"evidence": f"情绪状态影响价值观念"}
                        )
                        graph.add_edge(edge)
            
            # 添加话题与情绪的连接
            for topic_node in topic_nodes:
                for emotion_node in emotion_nodes:
                    if self._should_connect_topic_emotion(topic_node, emotion_node):
                        edge = GraphEdge(
                            source_node_id=topic_node.node_id,
                            target_node_id=emotion_node.node_id,
                            relation_type=RelationType.RELATED_TO,  # 修复：使用有效的关系类型
                            weight=0.5,
                            metadata={"evidence": f"话题引发情绪反应"}
                        )
                        graph.add_edge(edge)
            
            # 添加经历与信念的连接
            for exp_node in experience_nodes:
                for belief_node in belief_nodes:
                    if self._should_connect_experience_belief(exp_node, belief_node):
                        edge = GraphEdge(
                            source_node_id=exp_node.node_id,
                            target_node_id=belief_node.node_id,
                            relation_type=RelationType.LEADS_TO,
                            weight=0.7,
                            metadata={"evidence": f"经历塑造价值观"}
                        )
                        graph.add_edge(edge)
            
            logger.info(f"节点连接增强完成，边数：{len(graph.edges)}")
            return graph
            
        except Exception as e:
            logger.error(f"增强节点连接时发生错误: {e}")
            return graph
    
    def _should_connect_emotion_belief(self, emotion_node: GraphNode, belief_node: GraphNode) -> bool:
        """判断情绪和信念节点是否应该连接"""
        # 简单的连接逻辑，可以根据需要改进
        emotion_type = emotion_node.metadata.get("emotion_type", "")
        belief_content = belief_node.content.lower()
        
        # 基于情绪类型和信念内容的关联性
        if emotion_type == "anxiety" and any(word in belief_content for word in ["risk", "safety", "security"]):
            return True
        if emotion_type == "hope" and any(word in belief_content for word in ["goal", "future", "optimism"]):
            return True
        if emotion_type == "confusion" and any(word in belief_content for word in ["decision", "choice", "direction"]):
            return True
        
        return False
    
    def _should_connect_topic_emotion(self, topic_node: GraphNode, emotion_node: GraphNode) -> bool:
        """判断话题和情绪节点是否应该连接"""
        topic_content = topic_node.content.lower()
        emotion_type = emotion_node.metadata.get("emotion_type", "")
        
        # 基于话题内容和情绪类型的关联性
        if "career" in topic_content and emotion_type in ["anxiety", "confusion"]:
            return True
        if "relationship" in topic_content and emotion_type in ["sadness", "frustration"]:
            return True
        if "personal" in topic_content and emotion_type in ["hope", "determination"]:
            return True
        
        return False
    
    def _should_connect_experience_belief(self, exp_node: GraphNode, belief_node: GraphNode) -> bool:
        """判断经历和信念节点是否应该连接"""
        exp_content = exp_node.content.lower()
        belief_content = belief_node.content.lower()
        
        # 基于内容相似性判断
        common_keywords = ["work", "career", "relationship", "decision", "choice", "goal"]
        exp_keywords = [word for word in common_keywords if word in exp_content]
        belief_keywords = [word for word in common_keywords if word in belief_content]
        
        # 如果有共同关键词，则连接
        return len(set(exp_keywords) & set(belief_keywords)) > 0
    
    async def _build_user_profile(self, reddit_user: RedditUser,
                                user_graph: UserGraph,
                                semantic_analysis: Dict[str, Any]) -> UserProfile:
        """
        构建完整用户画像
        
        Args:
            reddit_user: Reddit用户数据
            user_graph: 用户图谱
            semantic_analysis: 语义分析结果
            
        Returns:
            UserProfile: 完整用户画像
        """
        try:
            # 创建基础用户画像
            user_profile = UserProfile(
                user_id=reddit_user.username,
                graph=user_graph,
                version="1.0"  # 修复：使用字符串而不是整数
            )
            
            # 计算完整性
            completeness = user_profile.update_completeness()
            
            # 添加额外的元数据
            user_profile.metadata = {
                "reddit_username": reddit_user.username,
                "total_posts": len(reddit_user.posts),
                "total_comments": len(reddit_user.comments),
                "analysis_timestamp": datetime.now(),
                "semantic_confidence": semantic_analysis.get("stats", {}).get("confidence_score", 0.5),
                "completeness_score": completeness
            }
            
            return user_profile
            
        except Exception as e:
            logger.error(f"构建用户画像时发生错误: {e}")
            # 创建基础画像
            return UserProfile(
                user_id=reddit_user.username,
                original_query=f"Reddit用户{reddit_user.username}的内容分析",
                graph=user_graph
            )
    
    async def _generate_graph_explanation(self, user_profile: UserProfile, 
                                        semantic_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成图谱解释
        
        Args:
            user_profile: 用户画像
            semantic_analysis: 语义分析结果
            
        Returns:
            Dict: 图谱解释
        """
        try:
            # 自定义JSON序列化器处理datetime对象
            def json_serial(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
            
            # 准备数据
            nodes_info = json.dumps([node.dict() for node in user_profile.graph.nodes.values()], indent=2, ensure_ascii=False, default=json_serial)
            edges_info = json.dumps([edge.dict() for edge in user_profile.graph.edges], indent=2, ensure_ascii=False, default=json_serial)
            semantic_context = json.dumps(semantic_analysis.get('parsed_query', {}).dict(), indent=2, ensure_ascii=False, default=json_serial)
            
            # 使用 Prompt Manager 生成 Prompt
            prompt_config = self.prompt_manager.get_full_prompt_config(
                PromptType.GRAPH_EXPLANATION,
                user_id=user_profile.user_id,
                node_count=len(user_profile.graph.nodes),
                edge_count=len(user_profile.graph.edges),
                quality_score=user_profile.metadata.get('graph_quality', {}).get('overall_score', 0),
                nodes_info=nodes_info,
                edges_info=edges_info,
                semantic_context=semantic_context
            )
            
            explanation_response = await self.ai_service.get_completion(
                prompt=prompt_config['prompt'],
                max_tokens=prompt_config['max_tokens'],
                temperature=prompt_config['temperature'],
                system_prompt=prompt_config['system_prompt']
            )

            # 提取解释
            explanation_text = explanation_response.strip()

            return {
                "explanation_text": explanation_text,
                "model_used": self.ai_service.default_model,
                "generated_at": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"生成图谱解释失败: {e}", exc_info=True)
            return {
                "error": str(e),
                "explanation_text": "生成图谱解释失败"
            }
    
    def _calculate_graph_quality(self, user_graph: UserGraph) -> Dict[str, Any]:
        """
        计算图谱质量指标
        
        Args:
            user_graph: 用户图谱
            
        Returns:
            Dict: 质量指标
        """
        try:
            node_count = len(user_graph.nodes)
            edge_count = len(user_graph.edges)
            node_types = user_graph.get_node_count_by_type()
            
            # 计算各种质量指标
            quality_metrics = {
                "node_count": node_count,
                "edge_count": edge_count,
                "node_types_distribution": node_types,
                "graph_density": edge_count / max(node_count * (node_count - 1), 1),
                "avg_node_degree": (2 * edge_count) / max(node_count, 1),
                "completeness_score": user_graph.calculate_completeness() if hasattr(user_graph, 'calculate_completeness') else 0.5,
                "structural_complexity": self._calculate_structural_complexity(user_graph),
                "information_richness": self._calculate_information_richness(user_graph),
                "overall_quality": "high" if node_count >= 10 and edge_count >= 5 else "medium" if node_count >= 5 else "low"
            }
            
            return quality_metrics
            
        except Exception as e:
            logger.error(f"计算图谱质量时发生错误: {e}")
            return {
                "node_count": 0,
                "edge_count": 0,
                "overall_quality": "low"
            }
    
    def _calculate_structural_complexity(self, user_graph: UserGraph) -> float:
        """计算结构复杂度"""
        try:
            node_count = len(user_graph.nodes)
            edge_count = len(user_graph.edges)
            
            if node_count == 0:
                return 0.0
            
            # 基于节点数和边数的复杂度
            complexity = (edge_count / node_count) / 3  # 标准化到0-1范围
            return min(complexity, 1.0)
            
        except Exception:
            return 0.0
    
    def _calculate_information_richness(self, user_graph: UserGraph) -> float:
        """计算信息丰富度"""
        try:
            # 基于节点类型多样性和内容长度
            node_types = set(node.node_type for node in user_graph.nodes.values())
            type_diversity = len(node_types) / 4  # 4种类型
            
            # 基于节点内容的平均长度
            avg_content_length = sum(len(node.content) for node in user_graph.nodes.values()) / len(user_graph.nodes)
            content_richness = min(avg_content_length / 100, 1.0)  # 标准化
            
            return (type_diversity + content_richness) / 2
            
        except Exception:
            return 0.0
    
    async def close(self):
        """关闭服务"""
        try:
            await self.ai_service.close()
            logger.info("人格图谱构建器已关闭")
        except Exception as e:
            logger.error(f"关闭人格图谱构建器时发生错误: {e}")

# 便捷函数
async def build_personality_graph(reddit_user: RedditUser, 
                                semantic_analysis: Dict[str, Any]) -> Dict[str, Any]:
    """
    便捷函数：构建人格图谱
    
    Args:
        reddit_user: Reddit用户数据
        semantic_analysis: 语义分析结果
        
    Returns:
        Dict: 图谱构建结果
    """
    builder = PersonalityGraphBuilder()
    try:
        result = await builder.build_personality_graph(reddit_user, semantic_analysis)
        return result
    finally:
        await builder.close()

if __name__ == "__main__":
    # 测试代码
    async def test_graph_builder():
        # 这里应该有实际的测试代码
        print("人格图谱构建器测试")
        
    # 运行测试
    # asyncio.run(test_graph_builder()) 