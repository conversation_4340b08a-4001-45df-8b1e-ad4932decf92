#!/usr/bin/env python3
"""
测试最终修复
"""
import requests
import json
import time

def test_reddit_analysis():
    """测试Reddit用户分析"""
    print("🧪 测试Reddit用户分析...")
    
    # 测试URL
    test_url = "https://www.reddit.com/r/cursor/comments/1lstb9t/comment/n1lgpq6/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button"
    
    try:
        # 发送分析请求
        response = requests.post(
            "http://127.0.0.1:5000/api/analyze",
            json={"url": test_url},
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get("success"):
                print("✅ 分析成功！")
                print(f"用户名: {result.get('username', 'N/A')}")
                print(f"图谱节点数: {result.get('graph_stats', {}).get('total_nodes', 'N/A')}")
                print(f"图谱边数: {result.get('graph_stats', {}).get('total_edges', 'N/A')}")
                print(f"置信度: {result.get('confidence_score', 'N/A')}")
                return True
            else:
                print(f"❌ 分析失败: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试最终修复...")
    
    # 等待应用完全启动
    print("等待应用启动...")
    time.sleep(3)
    
    # 测试健康检查
    try:
        response = requests.get("http://127.0.0.1:5000/", timeout=5)
        if response.status_code == 200:
            print("✅ 应用健康检查通过")
        else:
            print(f"⚠️ 应用响应异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 无法连接到应用: {e}")
        return
    
    # 执行分析测试
    success = test_reddit_analysis()
    
    if success:
        print("\n🎉 所有测试通过！修复成功！")
    else:
        print("\n⚠️ 测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
