#!/usr/bin/env python3
"""
直接测试图谱构建器的脚本，绕过Reddit数据抓取
"""

import asyncio
import sys
import os
import logging
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'only_profile'))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'resona'))

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from only_profile.graph_builder import PersonalityGraphBuilder, RedditUser, RedditPost, RedditComment

async def test_graph_builder_directly():
    """直接测试图谱构建器"""
    
    print("🔧 开始直接测试图谱构建器...")
    
    # 创建模拟的Reddit用户数据
    mock_user = RedditUser(
        username="test_user",
        posts=[
            RedditPost(
                id="post1",
                text="I love programming and building cool projects. Python is my favorite language.",
                timestamp=datetime.now(),
                subreddit="programming",
                score=25
            ),
            RedditPost(
                id="post2", 
                text="Feeling anxious about my job interview tomorrow. Any tips for dealing with stress?",
                timestamp=datetime.now(),
                subreddit="anxiety",
                score=12
            ),
            RedditPost(
                id="post3",
                text="Just finished reading a great book about mindfulness. It really changed my perspective on life.",
                timestamp=datetime.now(),
                subreddit="books",
                score=8
            )
        ],
        comments=[
            RedditComment(
                id="comment1",
                text="I think honesty and integrity are the most important values in life.",
                timestamp=datetime.now(),
                subreddit="philosophy",
                score=15
            ),
            RedditComment(
                id="comment2",
                text="Climate change is one of the biggest challenges we face. We need to act now.",
                timestamp=datetime.now(),
                subreddit="environment",
                score=22
            )
        ]
    )
    
    # 创建模拟的语义分析结果
    mock_semantic_analysis = {
        "success": True,
        "parsed_query": type('ParsedQuery', (), {
            'emotional_state': {
                'anxiety': 0.6,
                'excitement': 0.4,
                'curiosity': 0.7
            },
            'topics': ['programming', 'career', 'mindfulness', 'environment'],
            'values_info': {
                'honesty': 'very important',
                'integrity': 'core value',
                'environmental_responsibility': 'high priority'
            }
        })(),
        "user_characteristics": {
            "personality_traits": {
                "openness": 0.8,
                "conscientiousness": 0.7,
                "extraversion": 0.5,
                "agreeableness": 0.9,
                "neuroticism": 0.6
            },
            "interests_and_values": {
                "main_interests": ["programming", "reading", "environmental_issues", "personal_development"]
            }
        }
    }
    
    print("📊 模拟数据创建完成")
    print(f"  - 用户: {mock_user.username}")
    print(f"  - 帖子数: {len(mock_user.posts)}")
    print(f"  - 评论数: {len(mock_user.comments)}")
    print(f"  - 语义分析成功: {mock_semantic_analysis['success']}")
    
    # 创建图谱构建器
    try:
        graph_builder = PersonalityGraphBuilder()
        print("✅ 图谱构建器初始化成功")
    except Exception as e:
        print(f"❌ 图谱构建器初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 测试图谱构建
    try:
        print("\n🏗️ 开始构建人格图谱...")
        result = await graph_builder.build_personality_graph(mock_user, mock_semantic_analysis)
        
        if result['success']:
            print("✅ 图谱构建成功！")
            
            user_profile = result['user_profile']
            graph = user_profile.graph
            
            print(f"\n📈 图谱统计:")
            print(f"  - 节点数: {len(graph.nodes)}")
            print(f"  - 边数: {len(graph.edges)}")
            print(f"  - 完整性分数: {user_profile.completeness_score:.3f}")
            
            # 打印节点类型分布
            node_types = {}
            for node in graph.nodes.values():
                node_type = node.node_type.value
                node_types[node_type] = node_types.get(node_type, 0) + 1
            print(f"  - 节点类型分布: {node_types}")
            
            # 打印前几个节点的详情
            print(f"\n📍 节点详情 (前5个):")
            for i, (node_id, node) in enumerate(list(graph.nodes.items())[:5]):
                print(f"  {i+1}. {node_id}")
                print(f"     类型: {node.node_type.value}")
                print(f"     内容: {node.content[:60]}...")
                print(f"     权重: {node.weight:.3f}")
            
            # 打印边的详情
            if graph.edges:
                print(f"\n🔗 边详情 (前5条):")
                for i, edge in enumerate(graph.edges[:5]):
                    print(f"  {i+1}. {edge.source_node_id} -> {edge.target_node_id}")
                    print(f"     关系: {edge.relation_type.value}")
                    print(f"     权重: {edge.weight:.3f}")
            else:
                print(f"\n🔗 没有找到边")
            
            # 打印图谱解释
            if 'graph_explanation' in result:
                explanation = result['graph_explanation']
                print(f"\n📝 图谱解释:")
                print(f"  - 整体画像: {explanation.get('overall_portrait', 'N/A')[:100]}...")
                print(f"  - 关键特征数: {len(explanation.get('key_traits', []))}")
                print(f"  - 互动建议数: {len(explanation.get('interaction_recommendations', []))}")
            
            print(f"\n🎉 图谱构建测试完成！")
            
        else:
            print(f"❌ 图谱构建失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 图谱构建过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_graph_builder_directly())
