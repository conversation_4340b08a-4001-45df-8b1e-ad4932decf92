#!/usr/bin/env python3
"""
测试图谱构建修复
"""
import sys
import os
import json

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'only_profile'))

def test_context_handling():
    """测试上下文处理"""
    print("🧪 测试上下文处理...")
    
    try:
        from only_profile.graph_builder import PersonalityGraphBuilder
        
        builder = PersonalityGraphBuilder()
        
        # 模拟正确的上下文格式
        context = {"username": "test_user", "context": "测试上下文"}
        
        # 测试context.get()调用
        username = context.get('username', 'unknown')
        print(f"✅ 成功获取用户名: {username}")
        
        return True
        
    except Exception as e:
        print(f"❌ 上下文处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_parsing():
    """测试JSON解析"""
    print("\n🧪 测试JSON解析...")
    
    try:
        from only_profile.replicate_server import ReplicateAIService
        
        ai_service = ReplicateAIService()
        
        # 测试JSON提取
        mock_response = '{"test": "value", "nodes": [], "edges": []}'
        json_str = ai_service._extract_json_from_response(mock_response)
        parsed_data = json.loads(json_str)
        
        print(f"✅ JSON解析成功: {parsed_data}")
        return True
        
    except Exception as e:
        print(f"❌ JSON解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_node_type_parsing():
    """测试节点类型解析"""
    print("\n🧪 测试节点类型解析...")
    
    try:
        from only_profile.graph_builder import PersonalityGraphBuilder
        
        builder = PersonalityGraphBuilder()
        
        # 测试各种节点类型
        test_types = ["PERSONALITY", "INTEREST", "BELIEF", "EMOTION", "UNKNOWN_TYPE"]
        
        for type_str in test_types:
            node_type = builder._parse_node_type(type_str)
            print(f"✅ {type_str} -> {node_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ 节点类型解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试图谱构建修复...")
    
    tests = [
        ("上下文处理", test_context_handling),
        ("JSON解析", test_json_parsing),
        ("节点类型解析", test_node_type_parsing),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n📊 测试总结:")
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！修复成功！")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
