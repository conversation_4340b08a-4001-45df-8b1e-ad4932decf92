#!/usr/bin/env python3
"""
CogBridges Search - 启动脚本
快速启动应用的便捷脚本
"""

import sys
import os
import subprocess
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config, validate_startup_config


def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")
    
    required_packages = [
        'flask', 'flask_cors', 'requests', 'praw', 'asyncpraw',
        'google-api-python-client', 'tenacity', 'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        
        print(f"\n💡 请运行以下命令安装依赖:")
        print(f"   pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖已安装")
    return True


def check_configuration():
    """检查配置"""
    print("🔧 检查配置...")
    
    env_file = project_root / ".env"
    if not env_file.exists():
        print("⚠️ 未找到 .env 配置文件")
        print("💡 请复制 .env.example 为 .env 并填入配置:")
        print(f"   cp .env.example .env")
        return False
    
    # 验证配置
    if not validate_startup_config():
        return False
    
    return True


def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖...")
    
    requirements_file = project_root / "requirements.txt"
    if not requirements_file.exists():
        print("❌ 未找到 requirements.txt 文件")
        return False
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], check=True)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False


def run_tests():
    """运行测试"""
    print("🧪 运行测试...")
    
    test_script = project_root / "run_tests.py"
    if not test_script.exists():
        print("⚠️ 未找到测试脚本")
        return True  # 不阻止启动
    
    try:
        result = subprocess.run([
            sys.executable, str(test_script), "--unit-only", "-v", "1"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 单元测试通过")
            return True
        else:
            print("⚠️ 部分测试失败，但继续启动")
            print("💡 运行 'python run_tests.py' 查看详细测试结果")
            return True  # 不阻止启动
            
    except Exception as e:
        print(f"⚠️ 测试运行异常: {e}")
        return True  # 不阻止启动


def start_application():
    """启动应用"""
    print("🚀 启动 CogBridges Search 应用...")
    print(f"📍 访问地址: http://{config.HOST}:{config.PORT}")
    print("🛑 按 Ctrl+C 停止应用")
    print("-" * 50)
    
    try:
        # 导入并运行应用
        from app import app, initialize_services
        
        # 初始化服务
        if not initialize_services():
            print("❌ 服务初始化失败")
            return False
        
        # 启动Flask应用
        app.run(
            host=config.HOST,
            port=config.PORT,
            debug=config.FLASK_DEBUG,
            threaded=True
        )
        
        return True
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
        return True
    except Exception as e:
        print(f"❌ 应用启动失败: {e}")
        return False


def main():
    """主函数"""
    print("🌟 CogBridges Search - 启动向导")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version < (3, 7):
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        print("   需要Python 3.7或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查依赖
    if not check_dependencies():
        print("\n❓ 是否自动安装依赖? (y/n): ", end="")
        try:
            response = input().lower().strip()
            if response in ['y', 'yes', '是']:
                if not install_dependencies():
                    sys.exit(1)
            else:
                print("请手动安装依赖后重试")
                sys.exit(1)
        except KeyboardInterrupt:
            print("\n👋 启动已取消")
            sys.exit(1)
    
    # 检查配置
    if not check_configuration():
        print("\n💡 配置指南:")
        print("1. 复制 .env.example 为 .env")
        print("2. 编辑 .env 文件，填入以下配置:")
        print("   - Google Custom Search API密钥和搜索引擎ID")
        print("   - Reddit API客户端ID和密钥")
        print("   - 代理设置（如需要）")
        print("3. 重新运行此脚本")
        sys.exit(1)
    
    # 运行测试（可选）
    print("\n❓ 是否运行单元测试? (y/n): ", end="")
    try:
        response = input().lower().strip()
        if response in ['y', 'yes', '是']:
            run_tests()
    except KeyboardInterrupt:
        print("\n跳过测试...")
    
    print()
    
    # 启动应用
    if not start_application():
        sys.exit(1)


if __name__ == '__main__':
    main()
