#!/usr/bin/env python3
"""
测试完整的API流程，包括图谱序列化
"""

import asyncio
import sys
import os
import logging
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'only_profile'))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'resona'))

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from only_profile.app import RedditProfileApp

async def test_complete_api_flow():
    """测试完整的API流程"""
    
    print("🔧 开始测试完整的API流程...")
    
    # 创建应用实例
    try:
        app_instance = RedditProfileApp()
        # 确保组件初始化
        await app_instance._ensure_components_initialized()
        print("✅ 应用实例创建成功")
    except Exception as e:
        print(f"❌ 应用实例创建失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 创建模拟的Reddit用户数据
    from only_profile.graph_builder import RedditUser, RedditPost, RedditComment
    
    mock_user = RedditUser(
        username="test_api_user",
        posts=[
            RedditPost(
                id="post1",
                text="I believe in the importance of environmental protection and sustainable living.",
                timestamp=datetime.now(),
                subreddit="environment",
                score=25
            ),
            RedditPost(
                id="post2", 
                text="Programming has taught me patience and problem-solving skills.",
                timestamp=datetime.now(),
                subreddit="programming",
                score=12
            )
        ],
        comments=[
            RedditComment(
                id="comment1",
                text="I think honesty is the foundation of all good relationships.",
                timestamp=datetime.now(),
                subreddit="philosophy",
                score=15
            )
        ]
    )
    
    # 创建模拟的语义分析结果
    mock_semantic_analysis = {
        "success": True,
        "parsed_query": type('ParsedQuery', (), {
            'emotional_state': {
                'calm': 0.7,
                'determined': 0.8
            },
            'topics': ['environment', 'programming', 'philosophy'],
            'values_info': {
                'environmental_responsibility': 'high priority',
                'honesty': 'core value'
            }
        })(),
        "user_characteristics": {
            "personality_traits": {
                "openness": 0.8,
                "conscientiousness": 0.9,
                "extraversion": 0.5,
                "agreeableness": 0.8,
                "neuroticism": 0.3
            },
            "interests_and_values": {
                "main_interests": ["environment", "programming", "philosophy"]
            }
        },
        "stats": {
            "confidence_score": 0.85
        }
    }
    
    print("📊 模拟数据创建完成")
    
    # 测试图谱构建
    try:
        print("\n🏗️ 开始测试图谱构建...")
        graph_result = await app_instance.graph_builder.build_personality_graph(
            mock_user, mock_semantic_analysis
        )
        
        if graph_result['success']:
            print("✅ 图谱构建成功！")
            
            user_profile = graph_result['user_profile']
            print(f"  - 节点数: {len(user_profile.graph.nodes)}")
            print(f"  - 边数: {len(user_profile.graph.edges)}")
            print(f"  - 完整性分数: {user_profile.completeness_score:.3f}")
            
        else:
            print(f"❌ 图谱构建失败: {graph_result.get('error', '未知错误')}")
            return
            
    except Exception as e:
        print(f"❌ 图谱构建过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 测试图谱序列化
    try:
        print("\n📦 开始测试图谱序列化...")
        
        # 模拟构建最终结果（这会调用_convert_graph_for_frontend）
        mock_link_info = type('LinkInfo', (), {'link_type': type('LinkType', (), {'value': 'user'})()})()
        mock_crawl_result = {
            'username': 'test_api_user',
            'stats': {'total_posts': 2, 'total_comments': 1}
        }
        
        final_result = app_instance._build_final_result(
            url="https://reddit.com/u/test_api_user",
            link_info=mock_link_info,
            crawl_result=mock_crawl_result,
            semantic_result=mock_semantic_analysis,
            graph_result=graph_result
        )
        
        print("✅ 图谱序列化成功！")
        
        # 检查序列化结果
        if 'user_profile' in final_result and 'graph' in final_result['user_profile']:
            graph_data = final_result['user_profile']['graph']
            print(f"  - 序列化节点数: {len(graph_data.get('nodes', {}))}")
            print(f"  - 序列化边数: {len(graph_data.get('edges', []))}")
            
            # 检查边的属性
            if graph_data.get('edges'):
                first_edge = graph_data['edges'][0]
                print(f"  - 边属性检查: source_id={first_edge.get('source_id', 'N/A')}, target_id={first_edge.get('target_id', 'N/A')}")
                
                # 验证所有边都有正确的属性
                all_edges_valid = True
                for i, edge in enumerate(graph_data['edges']):
                    if 'source_id' not in edge or 'target_id' not in edge:
                        print(f"  ❌ 边 {i} 缺少必要属性: {edge}")
                        all_edges_valid = False
                
                if all_edges_valid:
                    print("  ✅ 所有边都有正确的属性")
                else:
                    print("  ❌ 部分边缺少必要属性")
            
            print(f"\n📋 完整结果结构:")
            print(f"  - 主要键: {list(final_result.keys())}")
            print(f"  - 用户画像键: {list(final_result['user_profile'].keys())}")
            print(f"  - 图谱键: {list(graph_data.keys())}")
            
        else:
            print("❌ 序列化结果中没有图谱数据")
            
    except Exception as e:
        print(f"❌ 图谱序列化过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print(f"\n🎉 完整API流程测试完成！")

if __name__ == "__main__":
    asyncio.run(test_complete_api_flow())
