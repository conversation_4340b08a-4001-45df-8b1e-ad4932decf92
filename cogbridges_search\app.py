"""
CogBridges Search - Flask应用主文件
提供Web界面和API接口
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, Any, List

from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_cors import CORS

from config import config, validate_startup_config
from services import GoogleSearchService, RedditService, DataService
from utils.logger_utils import get_logger, log_search_operation
from utils.proxy_utils import test_proxy_connection


# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = config.SECRET_KEY

# 启用CORS
if config.ENABLE_CORS:
    CORS(app, origins=config.ALLOWED_ORIGINS)

# 初始化日志
logger = get_logger(__name__)

# 全局服务实例
google_service = None
reddit_service = None
data_service = None


def initialize_services():
    """初始化所有服务"""
    global google_service, reddit_service, data_service
    
    try:
        logger.info("初始化服务...")
        
        # 初始化数据服务
        data_service = DataService()
        logger.info("✅ 数据服务初始化成功")
        
        # 初始化Google搜索服务
        if config.google_search_configured:
            google_service = GoogleSearchService()
            logger.info("✅ Google搜索服务初始化成功")
        else:
            logger.warning("⚠️ Google搜索服务未配置")
        
        # 初始化Reddit服务
        if config.reddit_configured:
            reddit_service = RedditService()
            logger.info("✅ Reddit服务初始化成功")
        else:
            logger.warning("⚠️ Reddit服务未配置")
        
        # 测试代理连接
        if config.proxy_configured:
            if test_proxy_connection():
                logger.info("✅ 代理连接测试成功")
            else:
                logger.warning("⚠️ 代理连接测试失败")
        
        logger.info("🚀 所有服务初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 服务初始化失败: {e}")
        return False


@app.route('/')
def index():
    """主页"""
    return render_template('index.html')


@app.route('/api/health')
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'services': {
            'google_search': google_service is not None,
            'reddit': reddit_service is not None,
            'data_storage': data_service is not None
        },
        'config': config.get_config_summary()
    })


@app.route('/api/search', methods=['POST'])
def search():
    """搜索接口"""
    try:
        data = request.get_json()
        
        if not data or 'query' not in data:
            return jsonify({'error': '缺少搜索查询参数'}), 400
        
        query = data['query'].strip()
        if len(query) < 3:
            return jsonify({'error': '搜索查询至少需要3个字符'}), 400
        
        max_results = data.get('max_results', config.GOOGLE_SEARCH_RESULTS_COUNT)
        reddit_only = data.get('reddit_only', True)
        
        if not google_service:
            return jsonify({'error': 'Google搜索服务未配置'}), 503
        
        logger.info(f"开始搜索: {query}")
        start_time = time.time()
        
        # 执行Google搜索
        if reddit_only:
            search_result = google_service.search_reddit_posts(query, max_results)
        else:
            search_result = google_service.search(query, max_results)
        
        if not search_result.success:
            return jsonify({
                'error': search_result.error_message,
                'success': False
            }), 500
        
        # 提取Reddit帖子URL
        post_urls = search_result.get_post_urls()
        
        # 生成会话ID
        session_id = data_service.generate_session_id(query)
        
        # 保存搜索结果
        data_service.save_search_result(search_result, session_id)
        
        # 记录搜索操作
        search_time = time.time() - start_time
        log_search_operation(query, len(search_result.results), search_time)
        
        return jsonify({
            'success': True,
            'session_id': session_id,
            'query': query,
            'total_results': len(search_result.results),
            'post_urls': post_urls,
            'search_time': search_time,
            'statistics': search_result.get_statistics()
        })
        
    except Exception as e:
        logger.error(f"搜索失败: {e}")
        return jsonify({'error': str(e), 'success': False}), 500


@app.route('/api/reddit/analyze', methods=['POST'])
def analyze_reddit():
    """Reddit数据分析接口"""
    try:
        data = request.get_json()
        
        if not data or 'post_urls' not in data:
            return jsonify({'error': '缺少帖子URL参数'}), 400
        
        post_urls = data['post_urls']
        session_id = data.get('session_id')
        
        if not post_urls:
            return jsonify({'error': '帖子URL列表为空'}), 400
        
        if not reddit_service:
            return jsonify({'error': 'Reddit服务未配置'}), 503
        
        logger.info(f"开始分析Reddit数据: {len(post_urls)} 个帖子")
        
        # 使用异步方法处理Reddit数据
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            reddit_data = loop.run_until_complete(
                reddit_service.process_search_results(post_urls)
            )
        finally:
            loop.close()
        
        if not reddit_data['success']:
            return jsonify({
                'error': 'Reddit数据处理失败',
                'success': False
            }), 500
        
        # 保存Reddit数据
        if session_id:
            data_service.save_reddit_data(reddit_data, session_id)
        
        return jsonify({
            'success': True,
            'session_id': session_id,
            'posts_data': reddit_data['posts_data'],
            'user_histories': reddit_data['user_histories'],
            'statistics': reddit_data['statistics']
        })
        
    except Exception as e:
        logger.error(f"Reddit分析失败: {e}")
        return jsonify({'error': str(e), 'success': False}), 500


@app.route('/api/suggestions')
def get_suggestions():
    """获取搜索建议"""
    try:
        query = request.args.get('q', '').strip()
        
        if len(query) < 2:
            return jsonify({'suggestions': []})
        
        if not google_service:
            return jsonify({'suggestions': []})
        
        suggestions = google_service.get_search_suggestions(query)
        
        return jsonify({'suggestions': suggestions})
        
    except Exception as e:
        logger.warning(f"获取搜索建议失败: {e}")
        return jsonify({'suggestions': []})


@app.route('/api/sessions')
def list_sessions():
    """列出搜索会话"""
    try:
        limit = request.args.get('limit', 20, type=int)
        sessions = data_service.list_sessions(limit)
        
        return jsonify({
            'success': True,
            'sessions': sessions,
            'count': len(sessions)
        })
        
    except Exception as e:
        logger.error(f"获取会话列表失败: {e}")
        return jsonify({'error': str(e), 'success': False}), 500


@app.route('/api/sessions/<session_id>')
def get_session(session_id):
    """获取特定会话数据"""
    try:
        session_data = data_service.load_session_data(session_id)
        
        if not session_data:
            return jsonify({'error': '会话不存在'}), 404
        
        return jsonify({
            'success': True,
            'session_data': session_data
        })
        
    except Exception as e:
        logger.error(f"获取会话数据失败: {e}")
        return jsonify({'error': str(e), 'success': False}), 500


@app.route('/api/sessions/<session_id>', methods=['DELETE'])
def delete_session(session_id):
    """删除会话"""
    try:
        success = data_service.delete_session(session_id)
        
        if success:
            return jsonify({'success': True, 'message': '会话已删除'})
        else:
            return jsonify({'error': '会话不存在'}), 404
        
    except Exception as e:
        logger.error(f"删除会话失败: {e}")
        return jsonify({'error': str(e), 'success': False}), 500


@app.route('/api/statistics')
def get_statistics():
    """获取系统统计信息"""
    try:
        stats = {
            'google_search': google_service.get_statistics() if google_service else {},
            'reddit': reddit_service.get_statistics() if reddit_service else {},
            'data_storage': data_service.get_storage_statistics() if data_service else {},
            'system': {
                'uptime': time.time() - app.start_time if hasattr(app, 'start_time') else 0,
                'config_summary': config.get_config_summary()
            }
        }
        
        return jsonify({
            'success': True,
            'statistics': stats
        })
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return jsonify({'error': str(e), 'success': False}), 500


@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({'error': '接口不存在'}), 404


@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    logger.error(f"内部服务器错误: {error}")
    return jsonify({'error': '内部服务器错误'}), 500


if __name__ == '__main__':
    # 验证配置
    if not validate_startup_config():
        logger.error("配置验证失败，应用无法启动")
        exit(1)
    
    # 初始化服务
    if not initialize_services():
        logger.error("服务初始化失败，应用无法启动")
        exit(1)
    
    # 记录启动时间
    app.start_time = time.time()
    
    # 启动应用
    logger.info(f"🚀 启动CogBridges Search应用")
    logger.info(f"📍 访问地址: http://{config.HOST}:{config.PORT}")
    
    app.run(
        host=config.HOST,
        port=config.PORT,
        debug=config.FLASK_DEBUG,
        threaded=True
    )
