#!/usr/bin/env python3
"""
单步全面分析测试
测试删除第一步后的单步全面分析效果
"""

import unittest
import sys
import os
import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List
from unittest.mock import Mock, patch, MagicMock, AsyncMock

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 导入测试模块
try:
    from only_profile.semantic_analyzer import ProfileSemanticAnalyzer
    from only_profile.models import RedditUser, RedditPost, RedditComment
except ImportError:
    # 如果导入失败，创建模拟类
    from typing import List, Dict, Any, Optional
    from pydantic import BaseModel, Field
    from datetime import datetime
    
    class RedditPost(BaseModel):
        id: str = ""
        text: str
        timestamp: datetime = Field(default_factory=datetime.now)
        subreddit: str = ""
        score: int = 0

    class RedditComment(BaseModel):
        id: str = ""
        text: str
        timestamp: datetime = Field(default_factory=datetime.now)
        subreddit: str = ""
        score: int = 0

    class RedditUser(BaseModel):
        username: str
        posts: List[RedditPost] = Field(default_factory=list)
        comments: List[RedditComment] = Field(default_factory=list)

    class ProfileSemanticAnalyzer:
        def __init__(self):
            pass
        
        async def analyze_user_content(self, reddit_user):
            return {"success": True, "mock": True}

class TestSingleStepAnalysis(unittest.TestCase):
    """单步全面分析测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.analyzer = ProfileSemanticAnalyzer()
        self.test_user = self._create_comprehensive_test_user()
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def _create_comprehensive_test_user(self) -> RedditUser:
        """创建全面测试用户数据"""
        
        # 创建多样化的帖子
        posts = [
            RedditPost(
                id="post1",
                text="刚刚完成了一个机器学习项目，感觉收获很大。使用了TensorFlow和Python，主要做图像识别。团队合作很重要，大家互相帮助才能完成这么复杂的任务。",
                timestamp=datetime.now() - timedelta(days=1),
                subreddit="MachineLearning",
                score=45
            ),
            RedditPost(
                id="post2", 
                text="最近在学习投资理财，发现复合增长的力量真的很强大。开始定投指数基金，希望能建立长期的财富积累计划。",
                timestamp=datetime.now() - timedelta(days=3),
                subreddit="investing",
                score=32
            ),
            RedditPost(
                id="post3",
                text="今天去爬山了，风景很美，心情也变好了。运动真的是减压的好方法，推荐大家多出去走走。",
                timestamp=datetime.now() - timedelta(days=7),
                subreddit="hiking",
                score=28
            ),
            RedditPost(
                id="post4",
                text="正在读《人类简史》这本书，作者的观点很有启发性。历史的发展确实充满了偶然性和必然性的交织。",
                timestamp=datetime.now() - timedelta(days=10),
                subreddit="books",
                score=67
            ),
            RedditPost(
                id="post5",
                text="工作压力有点大，最近加班比较多。但是项目很有挑战性，学到了很多新技术。生活就是这样，有得有失吧。",
                timestamp=datetime.now() - timedelta(days=14),
                subreddit="careerguidance",
                score=19
            )
        ]
        
        # 创建大量评论
        comments = []
        
        # 技术类评论
        tech_comments = [
            "这个算法的时间复杂度是O(n log n)，在大数据集上表现会比较好。",
            "建议使用Docker容器化部署，这样环境管理会更方便。",
            "Python的pandas库在数据处理方面确实很强大，配合matplotlib可以做出很好的可视化。",
            "代码review很重要，能发现很多潜在的bug和改进点。",
            "开源社区的力量真的很强大，大家都在为技术进步贡献自己的力量。"
        ]
        
        # 生活类评论
        life_comments = [
            "工作和生活的平衡确实很重要，不能因为工作而忽略了健康和家庭。",
            "学会说不也是一种能力，不能什么事情都答应下来。",
            "时间管理对提高效率很有帮助，推荐使用番茄工作法。",
            "保持学习的心态很重要，技术发展太快了，不学习就会被淘汰。",
            "身体健康是最重要的，定期体检和运动都不能少。"
        ]
        
        # 观点讨论类评论
        opinion_comments = [
            "我觉得这个观点有一定道理，但是需要考虑实际情况的复杂性。",
            "数据说话比主观判断更可靠，我们应该基于事实来分析问题。",
            "不同的人有不同的价值观，这很正常，关键是要相互理解和尊重。",
            "社会问题往往没有标准答案，需要多角度思考和讨论。",
            "教育的意义不仅仅是传授知识，更重要的是培养思维能力。"
        ]
        
        # 情感表达类评论
        emotion_comments = [
            "今天心情不错，解决了一个困扰很久的技术问题。",
            "有时候会感到迷茫，不知道自己的方向是否正确。",
            "朋友的支持真的很重要，感谢那些在困难时期帮助过我的人。",
            "失败并不可怕，关键是要从中学到经验教训。",
            "成功的喜悦是短暂的，更重要的是持续的成长和进步。"
        ]
        
        # 问题求助类评论
        help_comments = [
            "有人遇到过类似的问题吗？求解决方案。",
            "这个错误信息很奇怪，Google了很久都没找到答案。",
            "新手求指导，应该从哪里开始学习比较好？",
            "选择哪个框架比较合适？有经验的朋友给点建议。",
            "职业规划方面有什么建议吗？感觉有点迷茫。"
        ]
        
        # 知识分享类评论
        knowledge_comments = [
            "分享一个很有用的工具，可以大大提高开发效率。",
            "这篇文章写得很好，推荐大家阅读。",
            "总结了一些最佳实践，希望对大家有帮助。",
            "这个概念刚开始可能难理解，但是掌握后会很有用。",
            "实践中发现的一些技巧，分享给大家参考。"
        ]
        
        # 组合所有评论
        all_comment_texts = (
            tech_comments + life_comments + opinion_comments + 
            emotion_comments + help_comments + knowledge_comments
        )
        
        # 创建评论对象
        subreddits = [
            "Python", "MachineLearning", "programming", "cscareerquestions",
            "LifeProTips", "productivity", "books", "philosophy",
            "investing", "personalfinance", "fitness", "hiking"
        ]
        
        for i, text in enumerate(all_comment_texts):
            comments.append(
                RedditComment(
                    id=f"comment{i+1}",
                    text=text,
                    timestamp=datetime.now() - timedelta(hours=i*2),
                    subreddit=subreddits[i % len(subreddits)],
                    score=max(1, 30 - i)  # 分数递减
                )
            )
        
        # 添加更多随机评论以达到目标数量
        additional_comments = [
            "同意这个观点，很有道理。",
            "谢谢分享，学到了新知识。",
            "我也遇到过类似的情况。",
            "这个方法确实有效。",
            "支持楼主的想法。",
            "可以尝试这个解决方案。",
            "很好的建议，值得参考。",
            "我有不同的看法。",
            "需要更多的信息才能判断。",
            "期待更多的讨论。"
        ]
        
        for i, text in enumerate(additional_comments):
            comments.append(
                RedditComment(
                    id=f"additional_comment{i+1}",
                    text=text,
                    timestamp=datetime.now() - timedelta(minutes=i*30),
                    subreddit=subreddits[i % len(subreddits)],
                    score=max(1, 15 - i)
                )
            )
        
        return RedditUser(
            username="comprehensive_test_user",
            posts=posts,
            comments=comments
        )
    
    @patch('only_profile.semantic_analyzer.ReplicateAIService')
    async def test_single_step_comprehensive_analysis(self, mock_ai_service):
        """测试单步全面分析"""
        
        # 模拟AI响应
        mock_response = {
            "semantic_analysis": {
                "original_text": "技术专业人士，关注个人成长和生活平衡",
                "search_intent": ["技术学习", "职业发展", "生活方式", "投资理财"],
                "values_info": {
                    "core_values": ["持续学习", "工作生活平衡", "团队合作"],
                    "priorities": ["技术成长", "健康生活", "财务规划"],
                    "beliefs": ["实践出真知", "数据驱动决策", "终身学习"]
                },
                "emotional_state": {
                    "积极情绪": 0.7,
                    "消极情绪": 0.2,
                    "焦虑程度": 0.3,
                    "自信程度": 0.8,
                    "社交倾向": 0.6,
                    "压力水平": 0.4,
                    "满足感": 0.7,
                    "困惑程度": 0.3,
                    "好奇心": 0.9,
                    "决心": 0.8,
                    "幽默感": 0.5,
                    "同理心": 0.7
                },
                "topics": ["机器学习", "投资理财", "户外运动", "读书学习", "职业发展", "技术讨论", "生活哲学", "健康生活"],
                "core_concerns": ["技术能力提升", "职业发展方向", "工作生活平衡"],
                "decision_points": ["技术栈选择", "投资策略制定"],
                "life_domains": ["职业发展", "学习成长", "健康生活", "财务管理"],
                "support_needs": ["技术指导", "职业建议", "生活方式指导"],
                "confidence": 0.95
            },
            "user_characteristics": {
                "personality_traits": {
                    "开放性": 0.9,
                    "尽责性": 0.8,
                    "外向性": 0.6,
                    "宜人性": 0.7,
                    "神经质": 0.3,
                    "创新性": 0.8,
                    "分析性": 0.9,
                    "社交性": 0.6,
                    "实用性": 0.8,
                    "情感性": 0.5,
                    "独立性": 0.7,
                    "竞争性": 0.6,
                    "合作性": 0.8
                },
                "communication_style": {
                    "表达方式": ["理性", "直接", "友善"],
                    "语言特点": ["技术性", "逻辑清晰", "实用导向"],
                    "互动偏好": ["群体讨论", "知识分享", "问题解答"],
                    "信息分享": ["经验分享", "知识传播", "观点表达"]
                },
                "behavioral_patterns": {
                    "活跃时间": ["工作日", "晚上"],
                    "参与社区": ["技术类", "学习类", "生活类"],
                    "内容偏好": ["原创发布", "评论互动", "问答参与"],
                    "决策风格": ["理性分析", "数据驱动", "经验参考"]
                },
                "interests_and_expertise": {
                    "专业领域": ["机器学习", "软件开发"],
                    "兴趣爱好": ["阅读", "运动", "投资"],
                    "知识深度": {
                        "技术": 0.9,
                        "人文": 0.6,
                        "科学": 0.7,
                        "艺术": 0.3,
                        "体育": 0.5,
                        "商业": 0.6
                    }
                },
                "social_connections": {
                    "关系类型": ["同事", "学习伙伴", "网友"],
                    "社交范围": "适中",
                    "影响力": "中",
                    "社区贡献": ["知识分享", "问题解答", "经验传播"]
                },
                "life_stage_indicators": {
                    "年龄段": "25-35岁",
                    "职业状态": "技术工作者",
                    "生活阶段": "职业成长期",
                    "主要挑战": ["技术选择", "职业规划"]
                },
                "confidence_level": 0.9
            }
        }
        
        # 设置mock
        mock_ai_instance = AsyncMock()
        mock_ai_instance.call_ai.return_value = json.dumps(mock_response)
        mock_ai_service.return_value = mock_ai_instance
        
        # 执行测试
        self.logger.info("开始单步全面分析测试")
        start_time = datetime.now()
        
        result = await self.analyzer.analyze_user_content(self.test_user)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 验证结果
        self.assertTrue(result["success"])
        self.assertEqual(result["username"], "comprehensive_test_user")
        self.assertIn("parsed_query", result)
        self.assertIn("user_characteristics", result)
        self.assertIn("content_patterns", result)
        self.assertIn("analysis_report", result)
        
        # 验证分析策略
        self.assertEqual(result["stats"]["analysis_strategy"], "single_step_comprehensive")
        
        # 验证内容统计
        stats = result["stats"]
        self.assertEqual(stats["posts_analyzed"], 5)
        self.assertTrue(stats["comments_analyzed"] > 30)  # 应该有大量评论
        self.assertGreater(stats["content_length"], 1000)  # 内容长度应该很大
        
        # 验证AI调用
        mock_ai_instance.call_ai.assert_called_once()
        call_args = mock_ai_instance.call_ai.call_args[0][0]
        
        # 验证prompt包含所有内容
        self.assertIn("全面深度的用户画像分析", call_args)
        self.assertIn("machine learning", call_args.lower())  # 应该包含帖子内容
        self.assertIn("时间复杂度", call_args)  # 应该包含评论内容
        
        self.logger.info(f"单步全面分析测试完成，耗时: {duration:.2f}秒")
        self.logger.info(f"分析内容: {stats['posts_analyzed']}个帖子, {stats['comments_analyzed']}个评论")
        self.logger.info(f"内容长度: {stats['content_length']}字符")
        self.logger.info(f"置信度: {stats['confidence_score']}")
        
        return result
    
    async def test_content_preparation(self):
        """测试内容准备过程"""
        
        self.logger.info("测试内容准备过程")
        
        # 准备用户内容
        user_content = self.analyzer._prepare_user_content(self.test_user)
        
        # 验证内容包含性
        self.assertGreater(len(user_content), 1000)  # 内容应该很长
        self.assertIn("[帖子", user_content)  # 应该包含帖子
        self.assertIn("[评论", user_content)  # 应该包含评论
        
        # 统计内容
        posts_count = user_content.count("[帖子")
        comments_count = user_content.count("[评论")
        
        self.logger.info(f"准备的内容: {posts_count}个帖子, {comments_count}个评论")
        self.logger.info(f"总长度: {len(user_content)}字符")
        
        # 验证内容质量
        self.assertGreater(posts_count, 0)
        self.assertGreater(comments_count, 10)  # 应该有很多评论
        
        return user_content
    
    def test_comprehensive_content_selection(self):
        """测试全面内容选择"""
        
        self.logger.info("测试全面内容选择")
        
        # 组合所有内容
        all_content = self.test_user.posts + self.test_user.comments
        
        # 执行选择
        selected_content = self.analyzer._comprehensive_content_selection(all_content)
        
        # 验证选择结果
        self.assertGreater(len(selected_content), 20)  # 应该选择大量内容
        self.assertLessEqual(len(selected_content), self.analyzer.content_sample_size)
        
        # 验证包含帖子和评论
        posts_in_selected = [item for item in selected_content if isinstance(item, RedditPost)]
        comments_in_selected = [item for item in selected_content if isinstance(item, RedditComment)]
        
        self.assertGreater(len(posts_in_selected), 0)
        self.assertGreater(len(comments_in_selected), 0)
        
        self.logger.info(f"选择的内容: {len(posts_in_selected)}个帖子, {len(comments_in_selected)}个评论")
        self.logger.info(f"总选择: {len(selected_content)}项 (限制: {self.analyzer.content_sample_size})")

async def run_tests():
    """运行所有测试"""
    test_instance = TestSingleStepAnalysis()
    test_instance.setUp()
    
    print("=" * 80)
    print("单步全面分析测试报告")
    print("=" * 80)
    
    try:
        # 测试1: 内容准备
        print("\n1. 测试内容准备...")
        user_content = await test_instance.test_content_preparation()
        print(f"✅ 内容准备测试通过 - 长度: {len(user_content)}字符")
        
        # 测试2: 内容选择
        print("\n2. 测试内容选择...")
        test_instance.test_comprehensive_content_selection()
        print("✅ 内容选择测试通过")
        
        # 测试3: 单步全面分析
        print("\n3. 测试单步全面分析...")
        result = await test_instance.test_single_step_comprehensive_analysis()
        print("✅ 单步全面分析测试通过")
        
        print("\n" + "=" * 80)
        print("🎉 所有测试通过！")
        print("=" * 80)
        
        # 输出关键信息
        if result:
            stats = result["stats"]
            print(f"\n📊 分析统计:")
            print(f"   • 分析策略: {stats['analysis_strategy']}")
            print(f"   • 帖子数量: {stats['posts_analyzed']}")
            print(f"   • 评论数量: {stats['comments_analyzed']}")
            print(f"   • 内容长度: {stats['content_length']}字符")
            print(f"   • 置信度: {stats['confidence_score']:.2f}")
            
            if "parsed_query" in result:
                topics = result["parsed_query"].topics
                print(f"   • 识别主题: {len(topics)}个")
                print(f"   • 主要主题: {', '.join(topics[:3])}")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行测试
    asyncio.run(run_tests()) 