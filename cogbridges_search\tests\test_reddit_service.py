"""
CogBridges Search - Reddit服务单元测试
"""

import unittest
import asyncio
from unittest.mock import Mock, patch, AsyncMock, MagicMock
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.reddit_service import RedditService
from models.reddit_models import RedditPost, RedditComment, RedditUser, UserHistory
from config import config


class TestRedditService(unittest.TestCase):
    """Reddit服务测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 模拟配置
        self.original_client_id = config.REDDIT_CLIENT_ID
        self.original_client_secret = config.REDDIT_CLIENT_SECRET
        
        config.REDDIT_CLIENT_ID = "test_client_id"
        config.REDDIT_CLIENT_SECRET = "test_client_secret"
    
    def tearDown(self):
        """测试后清理"""
        config.REDDIT_CLIENT_ID = self.original_client_id
        config.REDDIT_CLIENT_SECRET = self.original_client_secret
    
    @patch('services.reddit_service.praw.Reddit')
    @patch('services.reddit_service.setup_proxy')
    def test_service_initialization(self, mock_setup_proxy, mock_praw):
        """测试服务初始化"""
        mock_setup_proxy.return_value = None
        mock_reddit_instance = Mock()
        mock_praw.return_value = mock_reddit_instance
        
        service = RedditService()
        
        self.assertIsNotNone(service.reddit)
        self.assertTrue(service.reddit.read_only)
        mock_praw.assert_called_once()
    
    def test_initialization_without_config(self):
        """测试未配置时的初始化"""
        config.REDDIT_CLIENT_ID = ""
        config.REDDIT_CLIENT_SECRET = ""
        
        with self.assertRaises(ValueError):
            RedditService()
    
    def test_parse_reddit_url_post(self):
        """测试Reddit帖子URL解析"""
        with patch('services.reddit_service.praw.Reddit'), \
             patch('services.reddit_service.setup_proxy'):
            service = RedditService()
        
        test_urls = [
            "https://www.reddit.com/r/Python/comments/abc123/test_post/",
            "https://reddit.com/r/programming/comments/def456/another_post/",
            "https://old.reddit.com/r/test/comments/ghi789/old_reddit_post/"
        ]
        
        for url in test_urls:
            result = service.parse_reddit_url(url)
            self.assertEqual(result['type'], 'post')
            self.assertIn('subreddit', result)
            self.assertIn('post_id', result)
    
    def test_parse_reddit_url_user(self):
        """测试Reddit用户URL解析"""
        with patch('services.reddit_service.praw.Reddit'), \
             patch('services.reddit_service.setup_proxy'):
            service = RedditService()
        
        test_urls = [
            "https://www.reddit.com/u/testuser",
            "https://reddit.com/user/anotheruser",
            "https://old.reddit.com/u/olduser/"
        ]
        
        for url in test_urls:
            result = service.parse_reddit_url(url)
            self.assertEqual(result['type'], 'user')
            self.assertIn('username', result)
    
    def test_parse_reddit_url_subreddit(self):
        """测试Reddit子版块URL解析"""
        with patch('services.reddit_service.praw.Reddit'), \
             patch('services.reddit_service.setup_proxy'):
            service = RedditService()
        
        test_urls = [
            "https://www.reddit.com/r/Python/",
            "https://reddit.com/r/programming",
            "https://old.reddit.com/r/test/"
        ]
        
        for url in test_urls:
            result = service.parse_reddit_url(url)
            self.assertEqual(result['type'], 'subreddit')
            self.assertIn('subreddit', result)
    
    def test_parse_reddit_url_invalid(self):
        """测试无效URL解析"""
        with patch('services.reddit_service.praw.Reddit'), \
             patch('services.reddit_service.setup_proxy'):
            service = RedditService()
        
        invalid_urls = [
            "https://example.com/not_reddit",
            "invalid_url",
            ""
        ]
        
        for url in invalid_urls:
            result = service.parse_reddit_url(url)
            if result:
                self.assertEqual(result['type'], 'unknown')
            else:
                self.assertIsNone(result)
    
    @patch('services.reddit_service.praw.Reddit')
    @patch('services.reddit_service.setup_proxy')
    def test_get_post_details(self, mock_setup_proxy, mock_praw):
        """测试获取帖子详情"""
        # 模拟Reddit帖子对象
        mock_submission = Mock()
        mock_submission.id = "test123"
        mock_submission.title = "Test Post Title"
        mock_submission.selftext = "This is test post content"
        mock_submission.author.name = "testuser"
        mock_submission.score = 100
        mock_submission.upvote_ratio = 0.95
        mock_submission.num_comments = 50
        mock_submission.created_utc = 1640995200
        mock_submission.subreddit.display_name = "test"
        mock_submission.permalink = "/r/test/comments/test123/test_post/"
        mock_submission.url = "https://reddit.com/r/test/comments/test123/"
        mock_submission.is_self = True
        mock_submission.pinned = False
        mock_submission.locked = False
        mock_submission.archived = False
        mock_submission.over_18 = False
        
        # 模拟异步Reddit客户端
        mock_async_reddit = AsyncMock()
        mock_async_reddit.submission.return_value = mock_submission
        
        mock_setup_proxy.return_value = None
        mock_praw.return_value = Mock()
        
        service = RedditService()
        service.async_reddit = mock_async_reddit
        service._async_initialized = True
        
        # 运行异步测试
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            post_url = "https://reddit.com/r/test/comments/test123/test_post/"
            result = loop.run_until_complete(service.get_post_details(post_url))
            
            self.assertIsNotNone(result)
            self.assertIsInstance(result, RedditPost)
            self.assertEqual(result.id, "test123")
            self.assertEqual(result.title, "Test Post Title")
            self.assertEqual(result.author, "testuser")
            self.assertEqual(result.score, 100)
            self.assertEqual(result.subreddit, "test")
        finally:
            loop.close()
    
    @patch('services.reddit_service.praw.Reddit')
    @patch('services.reddit_service.setup_proxy')
    def test_get_post_comments(self, mock_setup_proxy, mock_praw):
        """测试获取帖子评论"""
        # 模拟评论对象
        mock_comment1 = Mock()
        mock_comment1.id = "comment1"
        mock_comment1.body = "This is a test comment"
        mock_comment1.author.name = "commenter1"
        mock_comment1.score = 25
        mock_comment1.created_utc = 1640995300
        mock_comment1.parent_id = "t3_test123"
        mock_comment1.subreddit.display_name = "test"
        mock_comment1.permalink = "/r/test/comments/test123/test_post/comment1/"
        mock_comment1.is_submitter = False
        mock_comment1.replies = []
        
        mock_comment2 = Mock()
        mock_comment2.id = "comment2"
        mock_comment2.body = "Another test comment"
        mock_comment2.author.name = "commenter2"
        mock_comment2.score = 15
        mock_comment2.created_utc = 1640995400
        mock_comment2.parent_id = "t3_test123"
        mock_comment2.subreddit.display_name = "test"
        mock_comment2.permalink = "/r/test/comments/test123/test_post/comment2/"
        mock_comment2.is_submitter = False
        mock_comment2.replies = []
        
        # 模拟帖子对象
        mock_submission = Mock()
        mock_submission.comments = [mock_comment1, mock_comment2]
        mock_submission.comments.replace_more = AsyncMock()
        
        # 模拟异步Reddit客户端
        mock_async_reddit = AsyncMock()
        mock_async_reddit.submission.return_value = mock_submission
        
        mock_setup_proxy.return_value = None
        mock_praw.return_value = Mock()
        
        service = RedditService()
        service.async_reddit = mock_async_reddit
        service._async_initialized = True
        
        # 运行异步测试
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            post_url = "https://reddit.com/r/test/comments/test123/test_post/"
            result = loop.run_until_complete(service.get_post_comments(post_url, limit=2))
            
            self.assertEqual(len(result), 2)
            self.assertIsInstance(result[0], RedditComment)
            self.assertEqual(result[0].id, "comment1")
            self.assertEqual(result[0].author, "commenter1")
            self.assertTrue(result[0].is_top_level)
        finally:
            loop.close()
    
    def test_extract_commenters(self):
        """测试提取评论者"""
        with patch('services.reddit_service.praw.Reddit'), \
             patch('services.reddit_service.setup_proxy'):
            service = RedditService()
        
        comments = [
            RedditComment(
                id="1", body="Comment 1", author="user1", score=10,
                created_utc=1640995200, parent_id="t3_123", subreddit="test",
                permalink="/test1/"
            ),
            RedditComment(
                id="2", body="Comment 2", author="user2", score=5,
                created_utc=1640995300, parent_id="t3_123", subreddit="test",
                permalink="/test2/"
            ),
            RedditComment(
                id="3", body="Comment 3", author="user1", score=8,
                created_utc=1640995400, parent_id="t3_123", subreddit="test",
                permalink="/test3/"
            ),
            RedditComment(
                id="4", body="Comment 4", author="[deleted]", score=3,
                created_utc=1640995500, parent_id="t3_123", subreddit="test",
                permalink="/test4/"
            )
        ]
        
        commenters = service.extract_commenters(comments)
        
        self.assertEqual(len(commenters), 2)
        self.assertIn("user1", commenters)
        self.assertIn("user2", commenters)
        self.assertNotIn("[deleted]", commenters)
    
    @patch('services.reddit_service.praw.Reddit')
    @patch('services.reddit_service.setup_proxy')
    def test_test_api_connection(self, mock_setup_proxy, mock_praw):
        """测试API连接测试"""
        mock_subreddit = Mock()
        mock_subreddit.display_name = "test"
        
        mock_reddit = Mock()
        mock_reddit.subreddit.return_value = mock_subreddit
        
        mock_setup_proxy.return_value = None
        mock_praw.return_value = mock_reddit
        
        service = RedditService()
        result = service.test_api_connection()
        
        self.assertTrue(result)
    
    @patch('services.reddit_service.praw.Reddit')
    @patch('services.reddit_service.setup_proxy')
    def test_test_api_connection_failure(self, mock_setup_proxy, mock_praw):
        """测试API连接失败"""
        mock_reddit = Mock()
        mock_reddit.subreddit.side_effect = Exception("API Error")
        
        mock_setup_proxy.return_value = None
        mock_praw.return_value = mock_reddit
        
        service = RedditService()
        result = service.test_api_connection()
        
        self.assertFalse(result)
    
    @patch('services.reddit_service.praw.Reddit')
    @patch('services.reddit_service.setup_proxy')
    def test_get_statistics(self, mock_setup_proxy, mock_praw):
        """测试统计信息获取"""
        mock_setup_proxy.return_value = None
        mock_praw.return_value = Mock()
        
        service = RedditService()
        service.request_count = 10
        service.total_request_time = 25.0
        
        stats = service.get_statistics()
        
        self.assertEqual(stats['request_count'], 10)
        self.assertEqual(stats['total_request_time'], 25.0)
        self.assertEqual(stats['average_request_time'], 2.5)
        self.assertTrue(stats['api_configured'])


if __name__ == '__main__':
    unittest.main()
