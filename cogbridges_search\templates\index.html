<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CogBridges Search - Reddit智能搜索分析</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fab fa-reddit-alien"></i>
                    <span>CogBridges</span>
                </div>
                <div class="header-subtitle">
                    Reddit智能搜索分析平台
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Search Section -->
            <section class="search-section" id="searchSection">
                <div class="search-container">
                    <h1 class="search-title">探索Reddit的深度洞察</h1>
                    <p class="search-subtitle">
                        输入您的搜索查询，我们将为您分析Reddit上的相关讨论、热门评论和用户画像
                    </p>
                    
                    <form class="search-form" id="searchForm">
                        <div class="search-input-container">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search search-icon"></i>
                                <input 
                                    type="text" 
                                    class="search-input" 
                                    id="searchInput"
                                    placeholder="例如：python programming, career advice, startup tips..."
                                    autocomplete="off"
                                    spellcheck="false"
                                >
                                <button type="button" class="clear-button" id="clearButton">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            
                            <div class="search-options">
                                <div class="option-group">
                                    <label class="option-label">
                                        <input type="checkbox" id="redditOnly" checked>
                                        <span class="checkmark"></span>
                                        仅搜索Reddit
                                    </label>
                                </div>
                                
                                <div class="option-group">
                                    <label for="resultsCount">结果数量:</label>
                                    <select id="resultsCount" class="select-input">
                                        <option value="3">3个结果</option>
                                        <option value="5" selected>5个结果</option>
                                        <option value="8">8个结果</option>
                                        <option value="10">10个结果</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="search-buttons">
                            <button type="submit" class="btn btn-primary" id="searchButton">
                                <i class="fas fa-search"></i>
                                <span>开始搜索</span>
                            </button>
                            
                            <button type="button" class="btn btn-secondary" id="luckyButton">
                                <i class="fas fa-magic"></i>
                                <span>手气不错</span>
                            </button>
                        </div>
                    </form>
                    
                    <!-- Search Suggestions -->
                    <div class="search-suggestions" id="searchSuggestions" style="display: none;">
                        <div class="suggestions-list" id="suggestionsList"></div>
                    </div>
                </div>
            </section>

            <!-- Loading Section -->
            <section class="loading-section" id="loadingSection" style="display: none;">
                <div class="loading-container">
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                    </div>
                    <h2 class="loading-title">正在分析Reddit数据...</h2>
                    <div class="loading-steps">
                        <div class="step" id="step1">
                            <i class="fas fa-search"></i>
                            <span>搜索相关帖子</span>
                            <div class="step-status"></div>
                        </div>
                        <div class="step" id="step2">
                            <i class="fas fa-comments"></i>
                            <span>获取热门评论</span>
                            <div class="step-status"></div>
                        </div>
                        <div class="step" id="step3">
                            <i class="fas fa-users"></i>
                            <span>分析用户画像</span>
                            <div class="step-status"></div>
                        </div>
                        <div class="step" id="step4">
                            <i class="fas fa-chart-line"></i>
                            <span>生成分析报告</span>
                            <div class="step-status"></div>
                        </div>
                    </div>
                    <div class="loading-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="progress-text" id="progressText">0%</div>
                    </div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="results-container">
                    <!-- Results Header -->
                    <div class="results-header">
                        <h2 class="results-title">分析结果</h2>
                        <div class="results-meta">
                            <span class="query-display" id="queryDisplay"></span>
                            <span class="results-count" id="resultsCount"></span>
                            <span class="processing-time" id="processingTime"></span>
                        </div>
                        <div class="results-actions">
                            <button class="btn btn-outline" id="newSearchButton">
                                <i class="fas fa-search"></i>
                                新搜索
                            </button>
                            <button class="btn btn-outline" id="exportButton">
                                <i class="fas fa-download"></i>
                                导出数据
                            </button>
                        </div>
                    </div>

                    <!-- Results Content -->
                    <div class="results-content">
                        <!-- Statistics Overview -->
                        <div class="stats-overview" id="statsOverview">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="postsCount">0</div>
                                    <div class="stat-label">分析帖子</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-comments"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="commentsCount">0</div>
                                    <div class="stat-label">热门评论</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="usersCount">0</div>
                                    <div class="stat-label">用户画像</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-layer-group"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="subredditsCount">0</div>
                                    <div class="stat-label">子版块</div>
                                </div>
                            </div>
                        </div>

                        <!-- Results Tabs -->
                        <div class="results-tabs">
                            <div class="tab-nav">
                                <button class="tab-button active" data-tab="posts">
                                    <i class="fas fa-file-alt"></i>
                                    热门帖子
                                </button>
                                <button class="tab-button" data-tab="comments">
                                    <i class="fas fa-comments"></i>
                                    精选评论
                                </button>
                                <button class="tab-button" data-tab="users">
                                    <i class="fas fa-users"></i>
                                    用户分析
                                </button>
                                <button class="tab-button" data-tab="insights">
                                    <i class="fas fa-lightbulb"></i>
                                    深度洞察
                                </button>
                            </div>
                            
                            <div class="tab-content">
                                <div class="tab-pane active" id="postsTab">
                                    <div class="posts-list" id="postsList"></div>
                                </div>
                                
                                <div class="tab-pane" id="commentsTab">
                                    <div class="comments-list" id="commentsList"></div>
                                </div>
                                
                                <div class="tab-pane" id="usersTab">
                                    <div class="users-list" id="usersList"></div>
                                </div>
                                
                                <div class="tab-pane" id="insightsTab">
                                    <div class="insights-content" id="insightsContent"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Error Section -->
            <section class="error-section" id="errorSection" style="display: none;">
                <div class="error-container">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h2 class="error-title">出现了一些问题</h2>
                    <p class="error-message" id="errorMessage"></p>
                    <div class="error-actions">
                        <button class="btn btn-primary" id="retryButton">
                            <i class="fas fa-redo"></i>
                            重试
                        </button>
                        <button class="btn btn-secondary" id="backButton">
                            <i class="fas fa-arrow-left"></i>
                            返回搜索
                        </button>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-text">
                    <p>&copy; 2024 CogBridges Search. 基于Reddit API构建的智能分析平台。</p>
                </div>
                <div class="footer-links">
                    <a href="#" class="footer-link">关于</a>
                    <a href="#" class="footer-link">隐私政策</a>
                    <a href="#" class="footer-link">使用条款</a>
                    <a href="#" class="footer-link">API文档</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
