#!/usr/bin/env python3
"""
CogBridges 项目根入口。
允许在项目根目录直接执行 `python app.py` 启动 Reddit 用户画像分析服务。
"""
import os
import sys
import importlib.util


def main():
    """代理到 only_profile.start.main"""
    project_root = os.path.abspath(os.path.dirname(__file__))
    only_profile_dir = os.path.join(project_root, "only_profile")
    if only_profile_dir not in sys.path:
        sys.path.insert(0, only_profile_dir)

    # 延迟导入，避免影响参数解析
    from only_profile.start import main as start_main
    start_main()


# 动态加载 only_profile/app.py 并向外暴露 RedditProfileApp，
# 以兼容 only_profile.start 中的 `from app import RedditProfileApp` 引用。
try:
    _project_root = os.path.abspath(os.path.dirname(__file__))
    _only_profile_app_path = os.path.join(_project_root, "only_profile", "app.py")
    if os.path.exists(_only_profile_app_path):
        _spec = importlib.util.spec_from_file_location("_only_profile_app", _only_profile_app_path)
        _mod = importlib.util.module_from_spec(_spec)  # type: ignore
        if _spec.loader:  # pragma: no cover
            _spec.loader.exec_module(_mod)  # type: ignore
            # 将类挂载到当前模块命名空间，供 start.py 访问
            RedditProfileApp = getattr(_mod, "RedditProfileApp", None)  # type: ignore
except Exception as _e:  # pragma: no cover
    # 保底，如果加载失败则保持静默，稍后会在使用时暴露 ImportError
    RedditProfileApp = None  # type: ignore


if __name__ == "__main__":
    main() 