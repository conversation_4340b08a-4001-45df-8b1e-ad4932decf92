# CogBridges - Reddit User Profiler

**CogBridges Reddit 用户画像分析引擎** 是一个强大而精密的工具，旨在通过分析 Reddit 用户的公开言论，构建全面、深度、多维度的人格图谱和用户画像。

本项目利用先进的自然语言处理（NLP）和大型语言模型（LLM）技术，从用户的帖子和评论中提取深层信息，包括情感状态、价值观、兴趣领域、性格特征和行为模式，并将其可视化为直观的关系图谱。

## 核心功能

- **多维度链接解析**: 智能识别并处理 Reddit 用户主页、帖子和评论的链接。
- **异步数据抓取**: 基于 `asyncpraw` 构建的高效、异步的 Reddit 数据爬虫，能够快速获取指定用户的大量公开言论。
- **深度语义分析**:
    - 利用 LLM 对用户内容进行多层次的语义分析，提取话题、情感、意图和核心关切。
    - 结合心理学理论，对用户的性格特质、沟通风格、行为模式和价值观进行量化分析。
- **动态人格图谱构建**:
    - 将分析出的性格、兴趣、信念、情感等抽象概念具象化为图谱中的节点。
    - 智能构建节点之间的关系（如影响、支持、冲突），形成动态的人格网络。
- **Web 可视化界面**:
    - 提供一个简洁直观的前端界面，用于输入 Reddit 链接并发起分析。
    - 使用动态图表库（如 D3.js 或 ECharts）将生成的人格图谱进行可视化展示。
- **模块化 Prompt 管理**: 通过 `LLMPromptManager` 统一管理所有与 LLM 交互的 Prompt 和参数，提高可维护性和可扩展性。
- **全面的启动检查**: 内置环境检查脚本，确保 Python 版本、依赖、配置和端口均满足运行要求。

## 技术架构

系统采用模块化的设计，将数据抓取、分析、图谱构建和前端展示解耦。核心工作流程如下：

```mermaid
graph TD
    subgraph "用户交互层"
        A[前端界面 / API] --> B{启动与环境检查 start.py};
    end

    subgraph "应用服务层 (app.py)"
        B --> C[URL解析器 url_parser.py];
        C --> D[数据抓取器 data_crawler.py];
        D --> E[语义分析器 semantic_analyzer.py];
        E --> F[人格图谱构建器 graph_builder.py];
    end

    subgraph "AI 与数据核心"
        G[LLM Prompt管理器 llm_prompt_manager.py]
        H[AI服务调用器 replicate_server.py]
        I[SQLite 数据库]
        J[FAISS 向量索引]

        E --> G;
        F --> G;
        G --> H;
        F --> I[(存储图谱)];
        F --> J[(存储向量)];
    end

    subgraph "结果展示层"
        F --> K[可视化图谱];
        E --> L[分析报告];
        K --> A;
        L --> A;
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style H fill:#f8d,stroke:#333,stroke-width:2px
    style G fill:#f8d,stroke:#333,stroke-width:2px
```

## 模块说明

| 文件名                     | 职责                                                                                                                              |
| -------------------------- | --------------------------------------------------------------------------------------------------------------------------------- |
| `start.py`                 | **项目启动器**。负责环境检查、依赖验证、配置加载和启动 Flask 应用。是项目的唯一入口。                                               |
| `app.py`                   | **Web 应用核心**。基于 Flask 构建，提供 API 接口，调度和编排其他模块完成整个分析流程。                                              |
| `config.py`                | **主配置文件**。定义了项目的基础配置、模型参数、API 超时等。                                                                        |
| `local_config.py`          | **本地配置文件**。用于覆盖 `config.py` 中的默认设置，通常与 `.env` 文件结合使用。                                                   |
| `.env`                     | **环境变量文件**。存储敏感信息，如 API 密钥。**此文件不应提交到版本库**。                                                           |
| `url_parser.py`            | **链接解析器**。负责解析输入的 Reddit 链接，判断其类型（用户、帖子、评论）并提取关键信息。                                          |
| `data_crawler.py`          | **数据抓取器**。使用 `asyncpraw` 异步从 Reddit API 获取用户的帖子和评论数据。                                                       |
| `semantic_analyzer.py`     | **语义分析器**。调用 AI 服务对用户内容进行深度的语义和心理学分析，生成结构化的用户特征数据。                                        |
| `graph_builder.py`         | **人格图谱构建器**。基于语义分析结果，构建包含节点和边的用户人格图谱，并将其存储到数据库中。                                        |
| `llm_prompt_manager.py`    | **LLM Prompt 管理器**。集中管理所有用于调用大语言模型的 Prompt 模板、系统指令和模型参数，实现 Prompt 的模块化和复用。              |
| `replicate_server.py`      | **AI 服务调用封装**。封装了对 Replicate 等第三方 AI 推理服务的 API 调用，提供统一的接口。                                           |
| `requirements_core.txt`    | **核心依赖文件**。列出了项目运行所必需的 Python 包。                                                                                |
| `templates/index.html`     | **前端页面**。项目的主 HTML 文件，构成了用户交互界面。                                                                              |

## 安装与配置

1.  **克隆项目**
    ```bash
    git clone <repository_url>
    cd only_profile
    ```

2.  **创建并激活虚拟环境**
    ```bash
    python -m venv venv
    # Windows
    venv\Scripts\activate
    # macOS / Linux
    source venv/bin/activate
    ```

3.  **安装依赖**
    ```bash
    pip install -r requirements_core.txt
    ```

4.  **配置环境变量**
    - 复制或重命名 `.env.template` (如果存在) 为 `.env`。
    - 在 `.env` 文件中填入必要的 API 密钥，例如：
      ```env
      # Replicate API Token
      REPLICATE_API_TOKEN="r8_..."

      # Reddit API Credentials
      REDDIT_CLIENT_ID="your_client_id"
      REDDIT_CLIENT_SECRET="your_client_secret"
      REDDIT_USER_AGENT="your_user_agent"
      ```
    - **注意**: 如果没有 Reddit API 密钥，应用将以只读模式运行，可能会受限于 Reddit 的匿名访问策略。

## 如何运行

项目提供了统一的启动脚本 `start.py`，集成了环境检查和应用启动功能。

-   **仅检查环境**
    运行以下命令可以验证所有配置、依赖和目录是否正确，但不会启动 Web 应用。
    ```bash
    python start.py --check-only
    ```

-   **启动应用**
    ```bash
    python start.py
    ```
    默认情况下，应用会启动在 `http://127.0.0.1:5000`。

-   **自定义参数启动**
    可以指定主机、端口、日志级别或启用调试模式。
    ```bash
    python start.py --host 0.0.0.0 --port 8000 --debug --log-level DEBUG
    ```

## API 端点

| 方法   | 路径                  | 描述                                     |
| ------ | --------------------- | ---------------------------------------- |
| `GET`  | `/`                   | 返回主前端页面。                         |
| `POST` | `/api/analyze`        | **核心接口**。接收 Reddit URL 并返回完整的分析结果。 |
| `POST` | `/api/analyze_comment`| 分析单条评论的发布动机和理由。           |
| `GET`  | `/api/stats`          | 获取应用的运行统计信息，如成功率和总分析数。 |
| `GET`  | `/api/health`         | 健康检查接口，返回各组件的状态。         |
| `GET`  | `/api/last_result`    | 获取数据库中最近一次的成功分析结果。     |

---
*该文档由 CogBridges AI 自动生成和维护。* 