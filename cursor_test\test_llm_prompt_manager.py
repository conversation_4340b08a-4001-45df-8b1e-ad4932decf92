"""
LLM Prompt Manager 单元测试
"""

import unittest
import sys
import os
from unittest.mock import patch

# 将项目根目录添加到Python路径中，以便导入模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from only_profile.llm_prompt_manager import (
    LLMPromptManager,
    PromptType,
    SystemPromptType,
    LLMParameters,
    get_prompt_manager
)

class TestLLMPromptManager(unittest.TestCase):

    def setUp(self):
        """测试设置"""
        self.manager = LLMPromptManager()

    def test_singleton_instance(self):
        """测试 get_prompt_manager 是否返回单例"""
        manager1 = get_prompt_manager()
        manager2 = get_prompt_manager()
        self.assertIs(manager1, manager2)

    def test_get_system_prompt(self):
        """测试获取系统 Prompt"""
        psychologist_prompt = self.manager.get_system_prompt(SystemPromptType.PSYCHOLOGIST)
        self.assertIn("专业的心理分析师", psychologist_prompt)
        
        graph_builder_prompt = self.manager.get_system_prompt(SystemPromptType.GRAPH_BUILDER)
        self.assertIn("构建人格图谱", graph_builder_prompt)
        
        # 测试不存在的类型
        unknown_prompt = self.manager.get_system_prompt("unknown_type")
        self.assertEqual(unknown_prompt, "")

    def test_get_prompt_template(self):
        """测试获取 Prompt 模板"""
        template = self.manager.get_prompt_template(PromptType.COMPREHENSIVE_USER_ANALYSIS)
        self.assertIn("{user_content}", template)
        self.assertIn("{content_length}", template)
        
        # 测试不存在的模板
        unknown_template = self.manager.get_prompt_template("unknown_template")
        self.assertEqual(unknown_template, "")

    def test_get_default_parameters(self):
        """测试获取默认参数"""
        params = self.manager.get_default_parameters(PromptType.PERSONALITY_GRAPH_BUILDING)
        self.assertEqual(params.temperature, 0.3)
        self.assertEqual(params.max_tokens, 2500)
        self.assertEqual(params.system_prompt_type, SystemPromptType.GRAPH_BUILDER)
        
        # 测试不存在的配置
        default_params = self.manager.get_default_parameters("unknown_type")
        self.assertIsInstance(default_params, LLMParameters)
        self.assertEqual(default_params.temperature, 0.5) # 默认 dataclass 值

    def test_generate_prompt_success(self):
        """测试成功生成 Prompt"""
        prompt = self.manager.generate_prompt(
            PromptType.COMPREHENSIVE_USER_ANALYSIS,
            user_content="some user content",
            content_length=100
        )
        self.assertIn("some user content", prompt)
        self.assertIn("100", prompt)

    def test_generate_prompt_missing_params(self):
        """测试生成 Prompt 时缺少参数"""
        with self.assertRaises(ValueError):
            self.manager.generate_prompt(
                PromptType.COMPREHENSIVE_USER_ANALYSIS,
                user_content="some content"
                # 缺少 content_length
            )

    def test_get_full_prompt_config(self):
        """测试获取完整的 Prompt 配置"""
        config = self.manager.get_full_prompt_config(
            PromptType.PERSONALITY_GRAPH_BUILDING,
            username="testuser",
            content_count=10,
            content_summary="summary",
            topics=["ai", "python"],
            emotional_state={"joy": 0.8}
        )
        
        self.assertIn("prompt", config)
        self.assertIn("system_prompt", config)
        self.assertIn("temperature", config)
        self.assertIn("max_tokens", config)
        
        self.assertIn("testuser", config["prompt"])
        self.assertIn("专业的心理分析师", config["system_prompt"])
        self.assertEqual(config["temperature"], 0.3)
        self.assertEqual(config["max_tokens"], 2500)

    def test_update_and_export_import(self):
        """测试更新配置和导入导出功能"""
        # 更新系统 prompt
        new_psych_prompt = "You are a friendly bot."
        self.manager.update_system_prompt(SystemPromptType.PSYCHOLOGIST, new_psych_prompt)
        self.assertEqual(self.manager.get_system_prompt(SystemPromptType.PSYCHOLOGIST), new_psych_prompt)
        
        # 更新模板
        new_template = "Hello, {name}!"
        self.manager.update_prompt_template(PromptType.CONTENT_SUMMARIZATION, new_template)
        self.assertEqual(self.manager.get_prompt_template(PromptType.CONTENT_SUMMARIZATION), new_template)
        
        # 更新参数
        new_params = LLMParameters(temperature=0.99, max_tokens=100)
        self.manager.update_default_parameters(PromptType.EMOTION_ANALYSIS, new_params)
        self.assertEqual(self.manager.get_default_parameters(PromptType.EMOTION_ANALYSIS).temperature, 0.99)
        
        # 导出配置
        exported_config = self.manager.export_config()
        self.assertEqual(exported_config["system_prompts"]["psychologist"], new_psych_prompt)
        
        # 创建新实例并导入配置
        new_manager = LLMPromptManager()
        new_manager.import_config(exported_config)
        
        # 验证新实例中的配置
        self.assertEqual(new_manager.get_system_prompt(SystemPromptType.PSYCHOLOGIST), new_psych_prompt)
        self.assertEqual(new_manager.get_prompt_template(PromptType.CONTENT_SUMMARIZATION), new_template)
        self.assertEqual(new_manager.get_default_parameters(PromptType.EMOTION_ANALYSIS).temperature, 0.99)

    def test_validate_prompt_parameters(self):
        """测试验证 Prompt 参数"""
        is_valid = self.manager.validate_prompt_parameters(
            PromptType.COMPREHENSIVE_USER_ANALYSIS,
            user_content="content",
            content_length=100
        )
        self.assertTrue(is_valid)
        
        is_invalid = self.manager.validate_prompt_parameters(
            PromptType.COMPREHENSIVE_USER_ANALYSIS,
            user_content="content"
        )
        self.assertFalse(is_invalid)

    def test_get_prompt_info(self):
        """测试获取 Prompt 信息"""
        info = self.manager.get_prompt_info(PromptType.PERSONALITY_GRAPH_BUILDING)
        self.assertEqual(info["prompt_type"], "personality_graph_building")
        self.assertIsInstance(info["required_parameters"], list)
        self.assertIn("username", info["required_parameters"])
        self.assertIn("content_summary", info["required_parameters"])
        self.assertEqual(info["default_parameters"]["temperature"], 0.3)
        self.assertEqual(info["system_prompt_type"], "graph_builder")


if __name__ == '__main__':
    unittest.main() 