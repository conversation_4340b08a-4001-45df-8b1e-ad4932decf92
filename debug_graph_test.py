#!/usr/bin/env python3
"""
调试图谱构建的详细测试脚本
"""

import asyncio
import requests
import json
import time
import logging

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_graph_building():
    """测试图谱构建的详细过程"""
    
    print("🔍 开始详细调试图谱构建...")
    
    # 等待应用启动
    print("等待应用启动...")
    time.sleep(3)
    
    # 健康检查
    try:
        response = requests.get("http://127.0.0.1:5000/api/health", timeout=10)
        if response.status_code == 200:
            print("✅ 应用健康检查通过")
        else:
            print(f"❌ 应用健康检查失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 无法连接到应用: {e}")
        return

    # 测试Reddit用户分析
    print("🧪 开始详细测试Reddit用户分析...")

    test_data = {
        "url": "https://reddit.com/u/spez"  # Reddit CEO, should have lots of content
    }

    try:
        print(f"📤 发送请求: {test_data}")
        response = requests.post(
            "http://127.0.0.1:5000/api/analyze",
            json=test_data,
            timeout=120
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 分析请求成功！")
            
            # 详细打印结果
            print("\n📊 详细分析结果:")
            print(f"用户名: {result.get('username', 'N/A')}")
            print(f"分析状态: {result.get('success', 'N/A')}")
            print(f"错误信息: {result.get('error', 'N/A')}")

            # 检查用户画像数据
            if 'user_profile' in result and 'graph' in result['user_profile']:
                graph = result['user_profile']['graph']
                print(f"\n🕸️ 图谱信息:")
                print(f"  - 节点数: {len(graph.get('nodes', {}))}")
                print(f"  - 边数: {len(graph.get('edges', []))}")

                # 打印节点详情
                if graph.get('nodes'):
                    print(f"\n📍 节点详情:")
                    for node_id, node in graph['nodes'].items():
                        print(f"  - {node_id}: {node.get('node_type', 'N/A')} - {node.get('content', 'N/A')[:50]}...")

                # 打印边详情
                if graph.get('edges'):
                    print(f"\n🔗 边详情:")
                    for edge in graph['edges'][:5]:  # 只显示前5条边
                        print(f"  - {edge.get('source_node_id', 'N/A')} -> {edge.get('target_node_id', 'N/A')} ({edge.get('relation_type', 'N/A')})")

                # 打印节点类型统计
                node_types = {}
                for node in graph.get('nodes', {}).values():
                    node_type = node.get('node_type', 'unknown')
                    node_types[node_type] = node_types.get(node_type, 0) + 1
                print(f"\n📈 节点类型统计: {node_types}")

                # 打印完整性分数
                completeness = result['user_profile'].get('completeness_score', 'N/A')
                print(f"\n📊 完整性分数: {completeness}")

            else:
                print("❌ 响应中没有图谱数据")
                print(f"响应结构: {list(result.keys())}")
            
            # 检查语义分析数据
            if 'semantic_analysis' in result:
                semantic = result['semantic_analysis']
                print(f"\n🧠 语义分析信息:")
                print(f"  - 成功状态: {semantic.get('success', 'N/A')}")
                if 'parsed_query' in semantic:
                    parsed = semantic['parsed_query']
                    print(f"  - 情绪状态: {getattr(parsed, 'emotional_state', 'N/A') if hasattr(parsed, 'emotional_state') else 'N/A'}")
                    print(f"  - 话题: {getattr(parsed, 'topics', 'N/A') if hasattr(parsed, 'topics') else 'N/A'}")
                    print(f"  - 价值观: {getattr(parsed, 'values_info', 'N/A') if hasattr(parsed, 'values_info') else 'N/A'}")
            
            print(f"\n📋 完整响应结构:")
            print(f"响应键: {list(result.keys())}")
            
        else:
            print(f"❌ 分析失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_graph_building())
