# CogBridges Reddit用户画像分析 - 环境变量配置模板
# 复制此文件为 .env 并填入实际配置值

# =============================================================================
# AI服务配置 - Replicate
# =============================================================================
# Replicate API Token (必须)
# 获取地址：https://replicate.com/account/api-tokens
REPLICATE_API_TOKEN=your_replicate_api_token_here

# =============================================================================
# Reddit API配置 (可选，用于提高访问频率)
# =============================================================================
# Reddit应用配置
# 获取地址：https://www.reddit.com/prefs/apps
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here

# =============================================================================
# 服务器配置
# =============================================================================
HOST=127.0.0.1
PORT=5000
DEBUG=True

# =============================================================================
# 模型配置
# =============================================================================
# 主要分析模型
LLM_MODEL=openai/gpt-4.1-mini
# 嵌入模型
EMBEDDING_MODEL=replicate/all-mpnet-base-v2

# =============================================================================
# 分析配置
# =============================================================================
# Reddit历史记录抓取限制
REDDIT_HISTORY_LIMIT=200
# 最大图节点数
MAX_NODES_PER_TEXT=10
# 分析超时时间（秒）
ANALYSIS_TIMEOUT=300
