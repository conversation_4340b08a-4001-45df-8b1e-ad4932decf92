/* CogBridges Reddit用户画像分析 - 样式文件 */

/* 全局样式 */
:root {
    --primary-color: #FF4500;  /* Reddit橙色 */
    --secondary-color: #0079D3; /* Reddit蓝色 */
    --accent-color: #46D160;   /* 成功绿色 */
    --warning-color: #FFB000;  /* 警告黄色 */
    --danger-color: #EA0027;   /* 错误红色 */
    --light-gray: #F6F7F8;
    --medium-gray: #878A8C;
    --dark-gray: #1A1A1B;
    --border-radius: 8px;
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 25px rgba(0,0,0,0.15);
}

body {
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    background-color: var(--light-gray);
    color: var(--dark-gray);
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar {
    background-color: white !important;
    box-shadow: var(--shadow-sm);
}

.brand-text {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.nav-text {
    color: var(--medium-gray);
    font-weight: 500;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-title {
    color: var(--dark-gray);
    font-weight: 600;
    margin-bottom: 1rem;
}

/* 输入区域样式 */
.input-group {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.input-group-text {
    background-color: var(--light-gray);
    border: 1px solid #dee2e6;
    color: var(--primary-color);
}

.form-control {
    border: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 69, 0, 0.25);
}

/* 按钮样式 */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #e03d00;
    border-color: #e03d00;
    transform: translateY(-1px);
}

/* 示例链接样式 */
.example-links {
    padding: 0.5rem;
    background-color: var(--light-gray);
    border-radius: var(--border-radius);
}

.example-link {
    display: block;
    color: var(--secondary-color);
    font-size: 0.875rem;
    padding: 0.25rem 0;
    transition: color 0.3s ease;
}

.example-link:hover {
    color: var(--primary-color);
}

/* 状态提示样式 */
.alert {
    border-radius: var(--border-radius);
    border: none;
}

.alert-info {
    background-color: rgba(0, 121, 211, 0.1);
    color: var(--secondary-color);
}

/* 用户信息样式 */
.user-avatar {
    text-align: center;
    padding: 1rem 0;
}

.user-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    display: block;
}

.username {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-gray);
}

/* 统计网格样式 */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
    padding: 0.75rem;
    background-color: var(--light-gray);
    border-radius: var(--border-radius);
    transition: background-color 0.3s ease;
}

.stat-item:hover {
    background-color: #e8f0fe;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--medium-gray);
    font-weight: 500;
}

/* 标签容器样式 */
.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: var(--light-gray);
    color: var(--dark-gray);
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tag:hover {
    background-color: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.tag.emotion {
    background-color: rgba(234, 0, 39, 0.1);
    color: var(--danger-color);
}

.tag.topic {
    background-color: rgba(0, 121, 211, 0.1);
    color: var(--secondary-color);
}

.tag.personality {
    background-color: rgba(70, 209, 96, 0.1);
    color: var(--accent-color);
}

/* 质量指标样式 */
.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.metric-item:last-child {
    border-bottom: none;
}

.metric-label {
    font-size: 0.875rem;
    color: var(--medium-gray);
}

.metric-value {
    font-weight: 600;
    color: var(--dark-gray);
}

.quality-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
}

.badge.bg-success {
    background-color: var(--accent-color) !important;
}

.badge.bg-warning {
    background-color: var(--warning-color) !important;
}

.badge.bg-danger {
    background-color: var(--danger-color) !important;
}

/* 图谱容器样式 */
.graph-container {
    position: relative;
    width: 100%;
    height: 500px;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    overflow: hidden;
    background-color: white;
}

.graph-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: var(--medium-gray);
}

#graph-network {
    width: 100%;
    height: 100%;
}

/* 图谱控制按钮 */
.btn-group .btn-outline-secondary {
    border-color: #dee2e6;
    color: var(--medium-gray);
}

.btn-group .btn-outline-secondary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* 手风琴样式 */
.accordion-button {
    background-color: white;
    color: var(--dark-gray);
    font-weight: 500;
    border: none;
    padding: 1rem 1.25rem;
}

.accordion-button:not(.collapsed) {
    background-color: rgba(255, 69, 0, 0.1);
    color: var(--primary-color);
    box-shadow: none;
}

.accordion-button:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 69, 0, 0.25);
}

.accordion-body {
    padding: 1.25rem;
    background-color: #fafafa;
}

/* 节点详情模态框样式 */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    background-color: var(--light-gray);
    border-bottom: 1px solid #dee2e6;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.connections-list {
    max-height: 200px;
    overflow-y: auto;
}

.connection-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background-color: white;
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
}

.connection-type {
    padding: 0.125rem 0.5rem;
    background-color: var(--secondary-color);
    color: white;
    border-radius: 12px;
    font-size: 0.75rem;
    margin-right: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .graph-container {
        height: 400px;
    }
    
    .example-links {
        margin-top: 1rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: var(--border-radius) !important;
        margin-bottom: 0.25rem;
    }
}

@media (max-width: 576px) {
    .input-group {
        flex-direction: column;
    }
    
    .input-group .btn {
        border-radius: var(--border-radius);
        margin-top: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .graph-container {
        height: 300px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 图谱节点样式（通过JavaScript动态应用） */
.graph-node-experience {
    color: var(--primary-color);
}

.graph-node-belief {
    color: var(--secondary-color);
}

.graph-node-emotion {
    color: var(--danger-color);
}

.graph-node-topic {
    color: var(--accent-color);
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-gray);
    color: white;
    border-radius: var(--border-radius);
    padding: 0.5rem 0.75rem;
}

/* 页脚样式 */
footer {
    margin-top: auto;
    border-top: 1px solid #dee2e6;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .modal,
    footer {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .graph-container {
        border: 1px solid #dee2e6;
    }
}

/* 评论分析样式 */
.comment-analysis-section {
    padding: 0;
}

.comment-analysis-result {
    margin-top: 1rem;
    padding: 1rem;
    background-color: rgba(70, 209, 96, 0.05);
    border-radius: var(--border-radius);
    border: 1px solid rgba(70, 209, 96, 0.2);
}

.analysis-card {
    background-color: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid #e9ecef;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.analysis-card:hover {
    box-shadow: var(--shadow-sm);
    border-color: var(--primary-color);
}

.analysis-card .card-subtitle {
    color: var(--dark-gray);
    font-weight: 600;
    border-bottom: 2px solid var(--light-gray);
    padding-bottom: 0.5rem;
}

.analysis-card .small {
    line-height: 1.5;
}

.analysis-card .small div {
    margin-bottom: 0.5rem;
}

.analysis-card .small div:last-child {
    margin-bottom: 0;
}

#comment-analysis-loading {
    color: var(--medium-gray);
    padding: 2rem 0;
}

#comment-input {
    min-height: 80px;
    resize: vertical;
}

#comment-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 69, 0, 0.15);
}

#analyze-comment-btn {
    transition: all 0.3s ease;
}

#analyze-comment-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

#analyze-comment-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 分析结果样式 */
#reasoning-summary {
    font-style: italic;
    line-height: 1.5;
    color: var(--dark-gray);
}

#confidence-badge {
    background-color: var(--secondary-color) !important;
}

#motivation-badge {
    background-color: var(--accent-color) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .analysis-card {
        margin-bottom: 0.75rem;
        padding: 0.75rem;
    }
    
    .comment-analysis-result .row {
        margin-bottom: 0.5rem;
    }
    
    .comment-analysis-result .row:last-child {
        margin-bottom: 0;
    }
}

/* 目标内容样式 */
.target-content {
    background-color: rgba(0, 121, 211, 0.05) !important;
    border-color: var(--secondary-color) !important;
    font-style: italic;
}

.target-content-info {
    border-left: 4px solid var(--secondary-color);
}

.target-content-text {
    font-style: italic;
    line-height: 1.6;
    border-left: 3px solid var(--primary-color);
    margin-left: 1rem;
}

.alert-primary {
    background-color: rgba(0, 121, 211, 0.1);
    border-color: rgba(0, 121, 211, 0.3);
    color: #004085;
}

/* 目标内容分析标题样式 */
#comment-analysis-heading .accordion-button {
    font-weight: 600;
}

#comment-analysis-heading .accordion-button:not(.collapsed) {
    background-color: rgba(0, 121, 211, 0.1);
    color: var(--secondary-color);
} 