"""
LLM Prompt 管理器
统一管理所有 LLM 相关的 prompt 模板、参数配置和调用参数
"""

import logging
from typing import Dict, Any, List, Optional, Union
from enum import Enum
from dataclasses import dataclass
import json

logger = logging.getLogger(__name__)


class PromptType(str, Enum):
    """Prompt 类型枚举"""
    # 语义分析相关
    COMPREHENSIVE_USER_ANALYSIS = "comprehensive_user_analysis"
    COMMENT_REASONING_ANALYSIS = "comment_reasoning_analysis"
    
    # 图谱构建相关
    PERSONALITY_GRAPH_BUILDING = "personality_graph_building"
    GRAPH_EXPLANATION = "graph_explanation"
    
    # 通用分析
    CONTENT_SUMMARIZATION = "content_summarization"
    EMOTION_ANALYSIS = "emotion_analysis"


class SystemPromptType(str, Enum):
    """系统 Prompt 类型枚举"""
    PSYCHOLOGIST = "psychologist"
    SOCIAL_ANALYST = "social_analyst"
    GRAPH_BUILDER = "graph_builder"
    JSON_FORMATTER = "json_formatter"
    CONTENT_ANALYZER = "content_analyzer"


@dataclass
class LLMParameters:
    """LLM 调用参数配置"""
    temperature: float = 0.5
    max_tokens: int = 2000
    model: Optional[str] = None
    system_prompt_type: SystemPromptType = SystemPromptType.CONTENT_ANALYZER
    custom_system_prompt: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "model": self.model,
            "system_prompt_type": self.system_prompt_type.value,
            "custom_system_prompt": self.custom_system_prompt
        }


class LLMPromptManager:
    """LLM Prompt 管理器"""
    
    def __init__(self):
        """初始化 Prompt 管理器"""
        self._system_prompts = self._init_system_prompts()
        self._prompt_templates = self._init_prompt_templates()
        self._default_parameters = self._init_default_parameters()
        
        logger.info("LLM Prompt 管理器初始化完成")
    
    def _init_system_prompts(self) -> Dict[SystemPromptType, str]:
        """初始化系统 Prompt 库"""
        return {
            SystemPromptType.PSYCHOLOGIST: (
                "你是一个专业的心理分析师、社会学家和用户画像专家。"
                "请基于用户的所有Reddit发言内容进行全面深入的画像分析，"
                "确保分析质量与内容量相匹配。请严格按照JSON格式返回分析结果。"
            ),
            
            SystemPromptType.SOCIAL_ANALYST: (
                "你是一个专业的社会行为分析师，擅长理解人类的社交行为模式、"
                "沟通风格和人际关系动态。请基于用户内容进行客观、专业的分析。"
            ),
            
            SystemPromptType.GRAPH_BUILDER: (
                "你是一个专业的心理分析师，擅长构建人格图谱。"
                "请基于用户的行为和内容，构建反映其人格特征的节点关系图。"
                "请严格按照JSON格式返回结果。"
            ),
            
            SystemPromptType.JSON_FORMATTER: (
                "你是一个数据处理专家，擅长将分析结果格式化为结构化的JSON格式。"
                "请确保输出的JSON格式正确、完整，包含所有要求的字段。"
            ),
            
            SystemPromptType.CONTENT_ANALYZER: (
                "你是一个内容分析专家，擅长理解文本内容的深层含义、情感倾向和主题。"
                "请进行客观、准确的内容分析。"
            )
        }
    
    def _init_prompt_templates(self) -> Dict[PromptType, str]:
        """初始化 Prompt 模板库"""
        return {
            # 综合用户画像分析 Prompt
            PromptType.COMPREHENSIVE_USER_ANALYSIS: """
你是一个专业的心理分析师和用户画像专家。请基于以下Reddit用户的所有发言内容，进行全面深度的用户画像分析。

用户内容（{content_length}字符）：
{user_content}

请按照以下JSON格式返回分析结果，包含两个主要部分：

{{
    "semantic_analysis": {{
        "original_text": "用户内容综合概述",
        "search_intent": ["关键词1", "关键词2", "关键词3"],
        "values_info": {{
            "core_values": ["价值观1", "价值观2"],
            "priorities": ["优先级1", "优先级2"], 
            "beliefs": ["信念1", "信念2"]
        }},
        "emotional_state": {{
            "积极情绪": 0.0,
            "消极情绪": 0.0,
            "焦虑程度": 0.0,
            "自信程度": 0.0,
            "社交倾向": 0.0,
            "压力水平": 0.0,
            "满足感": 0.0,
            "困惑程度": 0.0,
            "好奇心": 0.0,
            "决心": 0.0,
            "幽默感": 0.0,
            "同理心": 0.0
        }},
        "topics": ["主题1", "主题2", "主题3", "主题4", "主题5", "主题6", "主题7", "主题8"],
        "core_concerns": ["核心关切1", "核心关切2", "核心关切3"],
        "decision_points": ["决策点1", "决策点2"],
        "life_domains": ["生活领域1", "生活领域2", "生活领域3"],
        "support_needs": ["支持需求1", "支持需求2"],
        "confidence": 0.95
    }},
    "user_characteristics": {{
        "personality_traits": {{
            "开放性": 0.0,
            "尽责性": 0.0,
            "外向性": 0.0,
            "宜人性": 0.0,
            "神经质": 0.0,
            "创新性": 0.0,
            "分析性": 0.0,
            "社交性": 0.0,
            "实用性": 0.0,
            "情感性": 0.0,
            "独立性": 0.0,
            "竞争性": 0.0,
            "合作性": 0.0
        }},
        "communication_style": {{
            "表达方式": ["直接", "委婉", "幽默", "理性", "感性"],
            "语言特点": ["正式", "随意", "技术性", "生活化"],
            "互动偏好": ["主动发起", "被动回应", "群体讨论", "一对一"],
            "信息分享": ["经验分享", "观点表达", "问题求助", "知识传播"]
        }},
        "behavioral_patterns": {{
            "活跃时间": ["工作日", "周末", "早晨", "深夜"],
            "参与社区": ["技术类", "生活类", "娱乐类", "学术类"],
            "内容偏好": ["原创发布", "转发分享", "评论互动", "问答参与"],
            "决策风格": ["理性分析", "直觉判断", "群体意见", "个人经验"]
        }},
        "interests_and_expertise": {{
            "专业领域": ["领域1", "领域2"],
            "兴趣爱好": ["爱好1", "爱好2", "爱好3"],
            "知识深度": {{
                "技术": 0.0,
                "人文": 0.0,
                "科学": 0.0,
                "艺术": 0.0,
                "体育": 0.0,
                "商业": 0.0
            }}
        }},
        "social_connections": {{
            "关系类型": ["同事", "朋友", "家人", "陌生人"],
            "社交范围": "广泛/有限",
            "影响力": "高/中/低",
            "社区贡献": ["内容创作", "知识分享", "情感支持", "问题解答"]
        }},
        "life_stage_indicators": {{
            "年龄段": "推测年龄段",
            "职业状态": "推测职业状态",
            "生活阶段": "推测生活阶段",
            "主要挑战": ["挑战1", "挑战2"]
        }},
        "confidence_level": 0.0
    }}
}}

请确保：
1. 所有数值在0.0-1.0之间
2. 列表包含具体、有意义的内容
3. 分析基于实际内容，不要虚构
4. 保持客观和专业的分析态度
5. 充分利用所有提供的内容进行深度分析
""",

            # 人格图谱构建 Prompt
            PromptType.PERSONALITY_GRAPH_BUILDING: """
请基于以下Reddit用户的发言内容，构建一个人格图谱。

用户信息：
- 用户名：{username}
- 发言数量：{content_count}

用户发言内容：
{content_summary}

语义分析结果：
- 主要话题：{topics}
- 情绪状态：{emotional_state}

请以JSON格式返回人格图谱，包含节点和边：
{{
    "nodes": [
        {{
            "node_id": "节点ID",
            "node_type": "PERSONALITY|INTEREST|BELIEF|EMOTION|BEHAVIOR",
            "content": "节点描述",
            "weight": 0.8,
            "metadata": {{}}
        }}
    ],
    "edges": [
        {{
            "source_id": "源节点ID",
            "target_id": "目标节点ID",
            "relation_type": "INFLUENCES|SUPPORTS|CONFLICTS_WITH|LEADS_TO|CAUSED_BY|RELATED_TO|DEPENDS_ON|REINFORCES",
            "weight": 0.7,
            "evidence": "关系证据"
        }}
    ]
}}

请确保：
1. 至少包含5-10个节点
2. 节点类型多样化
3. 边关系合理且有证据支持
4. 权重反映重要性
""",

            # 评论推理分析 Prompt
            PromptType.COMMENT_REASONING_ANALYSIS: """
请分析以下用户评论的背后动机和推理逻辑：

用户画像信息：
{user_profile}

评论内容：
"{comment_text}"

请以JSON格式返回分析结果：
{{
    "reasoning_analysis": {{
        "primary_motivation": "主要动机",
        "emotional_drivers": ["情感驱动因素1", "情感驱动因素2"],
        "cognitive_process": "认知过程分析",
        "underlying_values": ["潜在价值观1", "潜在价值观2"],
        "social_context": "社交背景分析",
        "decision_factors": ["决策因素1", "决策因素2"]
    }},
    "behavioral_insights": {{
        "communication_intent": "沟通意图",
        "response_pattern": "回应模式",
        "engagement_style": "参与风格",
        "influence_attempt": "影响意图"
    }},
    "psychological_indicators": {{
        "confidence_level": 0.8,
        "emotional_state": "情绪状态",
        "stress_indicators": ["压力指标1", "压力指标2"],
        "satisfaction_level": "满意度水平"
    }},
    "prediction": {{
        "likely_follow_up": "可能的后续行为",
        "interaction_preferences": ["互动偏好1", "互动偏好2"],
        "response_expectations": "期望的回应类型"
    }},
    "confidence": 0.85
}}

请基于用户画像和评论内容进行深度分析，解释用户的行为动机和心理状态。
""",

            # 图谱解释 Prompt
            PromptType.GRAPH_EXPLANATION: """
请为以下用户人格图谱生成详细的解释说明：

用户图谱信息：
- 用户ID: {user_id}
- 节点数量: {node_count}
- 边数量: {edge_count}
- 图谱质量: {quality_score}

图谱节点：
{nodes_info}

图谱关系：
{edges_info}

语义分析背景：
{semantic_context}

请生成一个全面的图谱解释，包括：
1. 整体人格特征概述
2. 关键节点和关系的解释
3. 行为模式分析
4. 发展建议

请以清晰、专业的方式组织内容，帮助理解用户的人格特征和行为模式。
""",

            # 内容摘要 Prompt
            PromptType.CONTENT_SUMMARIZATION: """
请对以下用户内容进行智能摘要：

原始内容长度：{content_length}字符
内容类型：{content_type}

原始内容：
{content}

请生成一个包含关键信息的摘要，保留重要的：
1. 核心观点和立场
2. 情感表达
3. 行为模式
4. 兴趣领域
5. 价值观表现

摘要应该简洁但信息丰富，长度控制在原始内容的20-30%。
""",

            # 情感分析 Prompt
            PromptType.EMOTION_ANALYSIS: """
请对以下内容进行详细的情感分析：

内容：
{content}

请以JSON格式返回情感分析结果：
{{
    "primary_emotions": {{
        "dominant_emotion": "主导情感",
        "intensity": 0.8,
        "positive_score": 0.7,
        "negative_score": 0.1,
        "neutral_score": 0.2
    }},
    "detailed_emotions": {{
        "joy": 0.6,
        "sadness": 0.1,
        "anger": 0.05,
        "fear": 0.1,
        "surprise": 0.1,
        "trust": 0.5,
        "disgust": 0.0,
        "anticipation": 0.3
    }},
    "emotion_trend": "情感趋势分析",
    "emotional_keywords": ["关键词1", "关键词2"]
}}
""",
        }
    
    def _init_default_parameters(self) -> Dict[PromptType, LLMParameters]:
        """初始化默认参数配置"""
        return {
            PromptType.COMPREHENSIVE_USER_ANALYSIS: LLMParameters(
                temperature=0.4,
                max_tokens=4000,
                system_prompt_type=SystemPromptType.PSYCHOLOGIST
            ),
            
            PromptType.COMMENT_REASONING_ANALYSIS: LLMParameters(
                temperature=0.4,
                max_tokens=2000,
                system_prompt_type=SystemPromptType.SOCIAL_ANALYST
            ),
            
            PromptType.PERSONALITY_GRAPH_BUILDING: LLMParameters(
                temperature=0.3,
                max_tokens=2500,
                system_prompt_type=SystemPromptType.GRAPH_BUILDER
            ),
            
            PromptType.GRAPH_EXPLANATION: LLMParameters(
                temperature=0.6,
                max_tokens=1500,
                system_prompt_type=SystemPromptType.CONTENT_ANALYZER
            ),
            
            PromptType.CONTENT_SUMMARIZATION: LLMParameters(
                temperature=0.5,
                max_tokens=1000,
                system_prompt_type=SystemPromptType.CONTENT_ANALYZER
            ),
            
            PromptType.EMOTION_ANALYSIS: LLMParameters(
                temperature=0.2,
                max_tokens=1000,
                system_prompt_type=SystemPromptType.JSON_FORMATTER
            )
        }
    
    def get_system_prompt(self, system_prompt_type: SystemPromptType) -> str:
        """获取系统 Prompt"""
        return self._system_prompts.get(system_prompt_type, "")
    
    def get_prompt_template(self, prompt_type: PromptType) -> str:
        """获取 Prompt 模板"""
        return self._prompt_templates.get(prompt_type, "")
    
    def get_default_parameters(self, prompt_type: PromptType) -> LLMParameters:
        """获取默认参数配置"""
        return self._default_parameters.get(prompt_type, LLMParameters())
    
    def generate_prompt(self, prompt_type: PromptType, **kwargs) -> str:
        """
        生成完整的 Prompt
        
        Args:
            prompt_type: Prompt 类型
            **kwargs: 模板参数
            
        Returns:
            生成的 Prompt 字符串
        """
        try:
            template = self.get_prompt_template(prompt_type)
            if not template:
                raise ValueError(f"未找到 Prompt 模板: {prompt_type}")
            
            # 格式化模板
            prompt = template.format(**kwargs)
            
            logger.debug(f"生成 {prompt_type} Prompt，长度: {len(prompt)}")
            return prompt
            
        except KeyError as e:
            logger.error(f"Prompt 模板参数缺失: {e}")
            raise ValueError(f"生成 Prompt 失败，缺少参数: {e}")
        except Exception as e:
            logger.error(f"生成 Prompt 失败: {e}")
            raise
    
    def get_full_prompt_config(self, prompt_type: PromptType, **kwargs) -> Dict[str, Any]:
        """
        获取完整的 Prompt 配置
        
        Args:
            prompt_type: Prompt 类型
            **kwargs: 模板参数
            
        Returns:
            包含 prompt、system_prompt 和参数的配置字典
        """
        try:
            # 获取参数配置
            params = self.get_default_parameters(prompt_type)
            
            # 生成主 Prompt
            prompt = self.generate_prompt(prompt_type, **kwargs)
            
            # 获取系统 Prompt
            system_prompt = params.custom_system_prompt or self.get_system_prompt(params.system_prompt_type)
            
            config = {
                "prompt": prompt,
                "system_prompt": system_prompt,
                "temperature": params.temperature,
                "max_tokens": params.max_tokens,
                "model": params.model,
                "prompt_type": prompt_type.value,
                "parameter_config": params.to_dict()
            }
            
            logger.debug(f"生成完整 Prompt 配置: {prompt_type}")
            return config
            
        except Exception as e:
            logger.error(f"获取完整 Prompt 配置失败: {e}")
            raise
    
    def update_system_prompt(self, system_prompt_type: SystemPromptType, new_prompt: str):
        """更新系统 Prompt"""
        self._system_prompts[system_prompt_type] = new_prompt
        logger.info(f"更新系统 Prompt: {system_prompt_type}")
    
    def update_prompt_template(self, prompt_type: PromptType, new_template: str):
        """更新 Prompt 模板"""
        self._prompt_templates[prompt_type] = new_template
        logger.info(f"更新 Prompt 模板: {prompt_type}")
    
    def update_default_parameters(self, prompt_type: PromptType, new_params: LLMParameters):
        """更新默认参数配置"""
        self._default_parameters[prompt_type] = new_params
        logger.info(f"更新默认参数: {prompt_type}")
    
    def list_available_prompts(self) -> List[str]:
        """列出所有可用的 Prompt 类型"""
        return [prompt_type.value for prompt_type in PromptType]
    
    def list_available_system_prompts(self) -> List[str]:
        """列出所有可用的系统 Prompt 类型"""
        return [system_type.value for system_type in SystemPromptType]
    
    def validate_prompt_parameters(self, prompt_type: PromptType, **kwargs) -> bool:
        """
        验证 Prompt 参数是否完整
        
        Args:
            prompt_type: Prompt 类型
            **kwargs: 模板参数
            
        Returns:
            是否通过验证
        """
        try:
            template = self.get_prompt_template(prompt_type)
            if not template:
                return False
            
            # 尝试格式化模板来检查参数
            template.format(**kwargs)
            return True
            
        except (KeyError, ValueError):
            return False
    
    def get_prompt_info(self, prompt_type: PromptType) -> Dict[str, Any]:
        """
        获取 Prompt 详细信息
        
        Args:
            prompt_type: Prompt 类型
            
        Returns:
            Prompt 信息字典
        """
        template = self.get_prompt_template(prompt_type)
        params = self.get_default_parameters(prompt_type)
        
        # 提取模板中的参数占位符
        import re
        placeholders = re.findall(r'\{(\w+)\}', template)
        
        return {
            "prompt_type": prompt_type.value,
            "template_length": len(template),
            "required_parameters": list(set(placeholders)),
            "default_parameters": params.to_dict(),
            "system_prompt_type": params.system_prompt_type.value
        }
    
    def export_config(self) -> Dict[str, Any]:
        """导出完整配置"""
        return {
            "system_prompts": {k.value: v for k, v in self._system_prompts.items()},
            "prompt_templates": {k.value: v for k, v in self._prompt_templates.items()},
            "default_parameters": {k.value: v.to_dict() for k, v in self._default_parameters.items()}
        }
    
    def import_config(self, config: Dict[str, Any]):
        """导入配置"""
        try:
            if "system_prompts" in config:
                for key, value in config["system_prompts"].items():
                    system_type = SystemPromptType(key)
                    self._system_prompts[system_type] = value
            
            if "prompt_templates" in config:
                for key, value in config["prompt_templates"].items():
                    prompt_type = PromptType(key)
                    self._prompt_templates[prompt_type] = value
            
            if "default_parameters" in config:
                for key, value in config["default_parameters"].items():
                    prompt_type = PromptType(key)
                    self._default_parameters[prompt_type] = LLMParameters(**value)
            
            logger.info("配置导入成功")
            
        except Exception as e:
            logger.error(f"配置导入失败: {e}")
            raise


# 全局实例
_prompt_manager = None

def get_prompt_manager() -> LLMPromptManager:
    """获取全局 Prompt 管理器实例"""
    global _prompt_manager
    if _prompt_manager is None:
        _prompt_manager = LLMPromptManager()
    return _prompt_manager


# 便捷函数
def get_prompt_config(prompt_type: PromptType, **kwargs) -> Dict[str, Any]:
    """获取 Prompt 配置的便捷函数"""
    return get_prompt_manager().get_full_prompt_config(prompt_type, **kwargs)


def generate_prompt(prompt_type: PromptType, **kwargs) -> str:
    """生成 Prompt 的便捷函数"""
    return get_prompt_manager().generate_prompt(prompt_type, **kwargs)


def get_system_prompt(system_prompt_type: SystemPromptType) -> str:
    """获取系统 Prompt 的便捷函数"""
    return get_prompt_manager().get_system_prompt(system_prompt_type)


def list_prompts() -> List[str]:
    """列出可用 Prompt 的便捷函数"""
    return get_prompt_manager().list_available_prompts()


if __name__ == "__main__":
    # 测试和演示
    manager = LLMPromptManager()
    
    print("=== LLM Prompt 管理器测试 ===")
    
    # 列出可用的 Prompt 类型
    print(f"可用 Prompt 类型: {manager.list_available_prompts()}")
    print(f"可用系统 Prompt 类型: {manager.list_available_system_prompts()}")
    
    # 测试生成综合分析 Prompt
    try:
        config = manager.get_full_prompt_config(
            PromptType.COMPREHENSIVE_USER_ANALYSIS,
            content_length=5000,
            user_content="测试用户内容..."
        )
        print(f"\n综合分析 Prompt 长度: {len(config['prompt'])}")
        print(f"系统 Prompt: {config['system_prompt'][:100]}...")
        print(f"参数配置: {config['parameter_config']}")
    except Exception as e:
        print(f"测试失败: {e}")
    
    print("\n=== 测试完成 ===") 